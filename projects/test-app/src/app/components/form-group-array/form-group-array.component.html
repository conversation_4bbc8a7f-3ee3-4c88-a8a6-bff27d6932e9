<app-base-field
  [label]="label()"
  [fieldNames]="[fieldName()]"
  [isReadOnly]="isReadOnly()"
  [useFieldset]="true"
>
  <div class="formArrayContainer">
    <div [formArrayName]="fieldName()">
      @for (control of controls(); let index = $index; track index;) {
      <div [formGroupName]="index" class="formArrayItem">
        <div class="formArrayItemHeader">
          <h4>Item {{ index + 1 }}</h4>
          <p-button
            icon="pi pi-times"
            [rounded]="true"
            [text]="true"
            severity="danger"
            (click)="removeItem(index)"
          />
        </div>
        <div>
          <!-- Custom template content -->
          <ng-container
            *ngTemplateOutlet="
            itemTemplate();
            context: {
              $implicit: control,
              fieldPrefix: `${fieldName()}.${index}`
            }
          "
          />
        </div>
      </div>
      }
    </div>
    <p-button icon="pi pi-plus" (click)="addItem()" label="Add Item" />
  </div>
</app-base-field>
