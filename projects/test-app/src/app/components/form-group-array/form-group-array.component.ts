import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  input,
  TemplateRef,
} from '@angular/core';
import { BaseFieldComponent } from '../base-field';
import {
  ControlContainer,
  FormArray,
  FormGroup,
  FormGroupDirective,
  NonNullableFormBuilder,
  ReactiveFormsModule,
} from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'app-form-group-array',
  imports: [
    ReactiveFormsModule,
    BaseFieldComponent,
    ButtonModule,
    NgTemplateOutlet,
  ],
  templateUrl: './form-group-array.component.html',
  styleUrl: './form-group-array.component.scss',
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree, required to use 'formControlName' within the component.
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormGroupArrayComponent extends BaseFieldComponent {
  controlContainer = inject(ControlContainer);
  fb = inject(NonNullableFormBuilder);

  // Factory function to create new form groups
  groupFactory = input.required<() => FormGroup>();
  // Template for rendering each group item
  itemTemplate = input.required<TemplateRef<unknown>>();

  get formGroup(): FormGroup {
    return this.controlContainer.control as FormGroup;
  }

  fieldName = computed(() => {
    const fieldNames = this.fieldNames();
    if (fieldNames.length > 1) {
      throw new Error(
        'FormGroupArrayComponent can only handle a single field name.'
      );
    }
    return fieldNames[0];
  });

  formArray = computed(() => {
    const field = this.formGroup.get(this.fieldName());
    if (field && field instanceof FormArray) {
      return field;
    }
    throw new Error(
      `Expected to find a FormArray for field name ${this.fieldName()}`
    );
  });

  controls = computed(() => {
    const array = this.formArray();
    const arrayControls = array.controls;
    if (!arrayControls || !Array.isArray(arrayControls)) {
      throw new Error(
        `Expected the formArray to contain a list of controls for field name ${this.fieldName()}`
      );
    }
    return arrayControls;
  });

  addItem() {
    const fieldArray = this.formArray();
    if (fieldArray) {
      const newGroup = this.groupFactory()();
      fieldArray.push(newGroup);
    }
  }

  removeItem(index: number) {
    const fieldArray = this.formArray();
    if (fieldArray) {
      fieldArray.removeAt(index);
    }
  }
}
