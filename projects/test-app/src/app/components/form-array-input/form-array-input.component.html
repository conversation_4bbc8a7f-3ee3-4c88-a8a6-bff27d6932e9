<app-base-field
  [label]="label()"
  [fieldNames]="[fieldName()]"
  [isReadOnly]="isReadOnly()"
  [useFieldset]="true"
>
  <div [formArrayName]="fieldName()">
    @for (entry of controls(); let index = $index; track index;) {
    <div>
      <input
        type="text"
        pInputText
        [id]="`${fieldName()}-#${index}`"
        [formControlName]="index"
        class="form-array-input"
      />
      <p-button
        icon="pi pi-times"
        [rounded]="true"
        [text]="true"
        severity="danger"
        (click)="removeItem(index)"
      />
    </div>
    }
  </div>
  <p-button icon="pi pi-plus" (click)="addItem()" />
</app-base-field>
