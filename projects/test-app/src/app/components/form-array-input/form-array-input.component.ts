import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
} from '@angular/core';
import {
  ControlContainer,
  FormArray,
  FormGroup,
  FormGroupDirective,
  NonNullableFormBuilder,
  ReactiveFormsModule,
} from '@angular/forms';
import { BaseFieldComponent } from '../base-field';
import { InputTextModule } from 'primeng/inputtext';
import { ButtonModule } from 'primeng/button';

@Component({
  selector: 'app-form-array-input',
  imports: [
    ReactiveFormsModule,
    BaseFieldComponent,
    InputTextModule,
    ButtonModule,
  ],
  templateUrl: './form-array-input.component.html',
  styleUrl: './form-array-input.component.css',
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree, required to use 'formControlName' within the component.
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormArrayInputComponent extends BaseFieldComponent {
  controlContainer = inject(ControlContainer);
  fb = inject(NonNullableFormBuilder);

  get formGroup(): FormGroup {
    return this.controlContainer.control as FormGroup;
  }

  fieldName = computed(() => {
    const fieldNames = this.fieldNames();
    if (fieldNames.length > 1) {
      throw new Error(
        'FormArrayInputComponent can only handle a single field name.'
      );
    }
    return fieldNames[0];
  });

  controls = computed(() => {
    const field = this.formGroup.get(this.fieldName());
    if (field && field instanceof FormArray) {
      return field.controls;
    }
    return [];
  });

  addItem() {
    const fieldArray = this.formGroup.get(this.fieldName());
    if (fieldArray && fieldArray instanceof FormArray) {
      fieldArray.push(this.fb.control(''));
    }
  }

  removeItem(index: number) {
    const fieldArray = this.formGroup.get(this.fieldName());
    if (fieldArray && fieldArray instanceof FormArray) {
      fieldArray.removeAt(index);
    }
  }
}
