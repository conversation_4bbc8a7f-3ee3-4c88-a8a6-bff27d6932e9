import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  input,
  signal,
} from '@angular/core';
import { PanelModule } from 'primeng/panel';
import { ControlContainer, FormGroup } from '@angular/forms';
import { isPresent } from '@helaba/iso20022-lib/util';
import { BaseFieldComponent } from '../base-field';
import { SelectButtonComponent } from '../select-button';
import { SelectOption } from '../../types';

const OPTION1_VALUE = 'option1';
const OPTION2_VALUE = 'option2';

function resetField(fieldName: string, formGroup: FormGroup) {
  const fieldControl = formGroup.get(fieldName);
  if (fieldControl) {
    fieldControl.reset();
    fieldControl.markAsTouched();
  }
}

@Component({
  selector: 'app-one-of-selector',
  imports: [BaseFieldComponent, SelectButtonComponent, PanelModule],
  templateUrl: './one-of-selector.component.html',
  styleUrl: './one-of-selector.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OneOfSelectorComponent {
  readonly OPTION1 = OPTION1_VALUE;
  readonly OPTION2 = OPTION2_VALUE;

  header = input.required<string>();
  option1Label = input.required<string>();
  option2Label = input.required<string>();
  option1Fields = input.required<string[]>();
  option2Fields = input.required<string[]>();
  isReadOnly = input.required<boolean>();

  controlContainer = inject(ControlContainer);

  get formGroup(): FormGroup {
    return this.controlContainer.control as FormGroup;
  }

  allFields = computed<string[]>(() => {
    return [...this.option1Fields(), ...this.option2Fields()];
  });

  selectButtonOptions = computed<SelectOption<string>[]>(() => {
    return [
      {
        key: this.option1Label(),
        label: this.option1Label(),
        value: this.OPTION1,
      },
      {
        key: this.option2Label(),
        label: this.option2Label(),
        value: this.OPTION2,
      },
    ];
  });

  selectedOption = signal<string | null>(null);

  // Only show errors if no option is selected. If an option is selected, the individual fields are shown, displaying their own errors.
  showErrors = computed(() => {
    return this.selectedOption() === null || this.selectedOption() === '';
  });

  onChangeSelectedOption(newSelectedOption: string) {
    this.selectedOption.set(newSelectedOption);

    if (newSelectedOption === this.OPTION1) {
      for (const field of this.option2Fields()) {
        resetField(field, this.formGroup);
      }
    } else if (newSelectedOption === this.OPTION2) {
      for (const field of this.option1Fields()) {
        resetField(field, this.formGroup);
      }
    }
  }

  // Compute whether the user has filled out option 1 or option 2 fields (or none) for readonly display.
  hasFilledOutOption1Fields = computed(() => {
    return this.option1Fields().some((field) => {
      const fieldControl = this.formGroup.get(field);
      return isPresent(fieldControl?.value);
    });
  });

  hasFilledOutOption2Fields = computed(() => {
    return this.option2Fields().some((field) => {
      const fieldControl = this.formGroup.get(field);
      return isPresent(fieldControl?.value);
    });
  });
}
