import { FormControl, FormGroup, NonNullableFormBuilder } from '@angular/forms';

export function getFormSchema(fb: NonNullableFormBuilder) {
  return {
    field1: fb.control(''),
    field2: fb.control(''),
    field3: fb.control(''),
    field4: fb.control(''),
    field5: fb.control(''),
    field6: fb.control(''),
    field7: fb.array<FormControl<string>>([]),
    field8: fb.control(''),
    field9: fb.control(''),
    field10: fb.control(''),
    field11: fb.array<
      FormGroup<{
        nestedField1: FormControl<string>;
        nestedField2: FormControl<string>;
      }>
    >([]),
  };
}

export function createField11Group(fb: NonNullableFormBuilder) {
  return fb.group({
    nestedField1: fb.control(''),
    nestedField2: fb.control(''),
  });
}

export type AppFormSchema = ReturnType<typeof getFormSchema>;

export function getFormGroup(fb: NonNullableFormBuilder) {
  const schema = getFormSchema(fb);
  return fb.group(schema);
}
