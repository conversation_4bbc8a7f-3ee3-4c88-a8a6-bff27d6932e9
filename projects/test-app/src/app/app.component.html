<div>
  <h2>Test Form</h2>

  <form
    [formGroup]="testFormGroup"
    [formRules]="rules"
    [affectedFields]="affectedFields"
    (ngSubmit)="onSubmit()"
  >
    <app-form-text-input
      label="Field 1"
      [fieldNames]="['field1']"
      [isReadOnly]="false"
    />
    <app-form-text-input
      label="Field 2"
      [fieldNames]="['field2']"
      [isReadOnly]="false"
    />
    <app-one-of-selector
      header="Select between field 3 and field 4"
      option1Label="Field 3"
      option2Label="Field 4"
      [option1Fields]="['field3']"
      [option2Fields]="['field4']"
      [isReadOnly]="false"
    >
      <ng-container slot="option1">
        <app-form-text-input
          label="Field 3"
          [fieldNames]="['field3']"
          [isReadOnly]="false"
        />
      </ng-container>
      <ng-container slot="option2">
        <app-form-text-input
          label="Field 4"
          [fieldNames]="['field4']"
          [isReadOnly]="false"
        />
      </ng-container>
    </app-one-of-selector>
    <app-form-text-input
      label="Field 5"
      [fieldNames]="['field5']"
      [isReadOnly]="false"
    />
    <app-form-text-input
      label="Field 6"
      [fieldNames]="['field6']"
      [isReadOnly]="false"
    />
    <app-form-array-input
      label="Field 7"
      [fieldNames]="['field7']"
      [isReadOnly]="false"
    />
    <app-form-text-input
      label="Field 8"
      [fieldNames]="['field8']"
      [isReadOnly]="false"
    />
    <app-form-text-input
      label="Field 9"
      [fieldNames]="['field9']"
      [isReadOnly]="false"
    />
    <app-form-text-input
      label="Field 10"
      [fieldNames]="['field10']"
      [isReadOnly]="false"
    />
    <app-form-group-array
      label="Field 11"
      [fieldNames]="['field11']"
      [isReadOnly]="false"
      [groupFactory]="getField11GroupFactory()"
      [itemTemplate]="field11Template"
    />
    <div>
      <button type="submit">Submit</button>
    </div>
  </form>
</div>

<ng-template #field11Template let-control let-fieldPrefix="fieldPrefix">
  <div [formGroup]="control">
    <app-form-text-input
      label="Nested Field 1"
      [fieldNames]="['nestedField1']"
      [isReadOnly]="false"
      [fieldPrefix]="fieldPrefix"
    />
    <app-form-text-input
      label="Nested Field 2"
      [fieldNames]="['nestedField2']"
      [isReadOnly]="false"
      [fieldPrefix]="fieldPrefix"
    />
  </div>
</ng-template>
