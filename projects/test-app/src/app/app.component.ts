import {
  Component,
  ChangeDetectionStrategy,
  inject,
  effect,
} from '@angular/core';
import { NonNullableFormBuilder, ReactiveFormsModule } from '@angular/forms';
import {
  FormRulesDirective,
  AutohideDirective,
} from '@helaba/iso20022-lib/directives';
import { Rule } from '@helaba/iso20022-lib/rules';
import { FormErrorsService } from '@helaba/iso20022-lib/error';
import {
  affectedFields,
  errorMessages,
  testValidationRules,
} from './test-validation-rules';
import { createField11Group, getFormGroup } from './app.form';
import { OneOfSelectorComponent } from './components/one-of-selector/one-of-selector.component';
import { FormTextInputComponent } from './components';
import { FormArrayInputComponent } from './components/form-array-input';
import { FormGroupArrayComponent } from './components/form-group-array';

@Component({
  selector: 'app-root',
  imports: [
    ReactiveFormsModule,
    FormRulesDirective,
    OneOfSelectorComponent,
    FormTextInputComponent,
    FormArrayInputComponent,
    FormGroupArrayComponent,
  ],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AppComponent {
  fb = inject(NonNullableFormBuilder);
  formErrorsService = inject(FormErrorsService);

  testFormGroup = getFormGroup(this.fb);
  rules: Rule<string, undefined>[] = testValidationRules;
  affectedFields = affectedFields;

  constructor() {
    effect(() => {
      this.formErrorsService.setErrorMessages(errorMessages);
    });
  }

  onSubmit() {
    for (const key in this.testFormGroup.controls) {
      const control = this.testFormGroup.get(key);
      control?.updateValueAndValidity();
      control?.markAsTouched();
    }
    if (this.testFormGroup.valid) {
      console.log('Submitting', this.testFormGroup.getRawValue());
    }
  }

  getField11GroupFactory() {
    return () => createField11Group(this.fb);
  }
}
