{"name": "test-app", "version": "0.0.0", "private": true, "scripts": {"ng": "ng", "start": "ng serve"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@angular/common": "^19.2.14", "@angular/core": "^19.2.14", "@angular/forms": "^19.2.14", "@angular/platform-browser": "^19.2.14", "@angular/platform-browser-dynamic": "^19.2.14", "@angular/router": "^19.2.14", "@helaba/iso20022-lib": "file:../../dist/helaba-iso20022-lib-0.0.1.tgz", "@primeng/themes": "^19.1.3", "primeicons": "^7.0.0", "primeng": "^19.1.3", "rxjs": "^7.8.2", "zone.js": "^0.15.1"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.11", "@angular/cli": "^19.2.9", "@angular/compiler": "^19.2.14", "@angular/compiler-cli": "^19.2.0", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0"}}