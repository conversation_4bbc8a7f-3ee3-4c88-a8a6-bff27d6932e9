import {
  ChangeDetectionStrategy,
  Component,
  computed,
  contentChild,
  effect,
  inject,
  input,
  Signal,
  TemplateRef,
} from '@angular/core';
import { NgTemplateOutlet } from '@angular/common';
import { AbstractControl, ControlContainer, FormGroup } from '@angular/forms';
import { Subscription } from 'rxjs';
import { FormErrorsService } from '../form-errors-service';

@Component({
  selector: 'field-errors',
  imports: [NgTemplateOutlet],
  templateUrl: './field-errors.component.html',
  styleUrl: './field-errors.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FieldErrorsComponent {
  fieldName = input.required<string>();

  controlContainer = inject(ControlContainer);
  #formErrorsService = inject(FormErrorsService);

  get formGroup(): FormGroup {
    return this.controlContainer.control as FormGroup;
  }

  control = computed<AbstractControl | null>(() => {
    if (!this.formGroup) {
      console.warn(
        'FieldErrorsComponent: formGroup is not defined. Ensure that this component is used within a form context.'
      );
    }
    return this.formGroup.get(this.fieldName());
  });

  errorTemplate = contentChild<TemplateRef<{ $implicit: string }>>(TemplateRef);

  // To keep track of the subscription to the control's statusChanges.
  #activeSubscriptions: Subscription[] = [];

  constructor() {
    effect((onCleanup) => {
      // Clean up any existing subscription when the control changes or component is destroyed.
      this.cleanup();

      const currentControl = this.control();

      if (currentControl) {
        this.#activeSubscriptions.push(
          currentControl.statusChanges.subscribe(() => {
            const errors = currentControl.errors;

            // Update the errors in the central FormErrorsService.
            const erroneousRules = errors ? Object.keys(errors) : null;
            this.#formErrorsService.updateFieldErrors(
              this.fieldName(),
              erroneousRules
            );
          })
        );
      } else {
        this.#formErrorsService.updateFieldErrors(this.fieldName(), []);
      }

      onCleanup(() => {
        this.cleanup();
      });
    });
  }

  errorMessages: Signal<string[]> = computed(() => {
    const fieldErrors =
      this.#formErrorsService.fieldErrors().get(this.fieldName()) || [];
    return fieldErrors.map(
      (ruleId) => this.#formErrorsService.errorMessages().get(ruleId) || ruleId
    );
  });

  private cleanup() {
    for (const sub of this.#activeSubscriptions) {
      sub.unsubscribe();
    }
    this.#activeSubscriptions = [];
  }
}
