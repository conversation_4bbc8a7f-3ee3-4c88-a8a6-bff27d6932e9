import { computed, Injectable, signal } from '@angular/core';
import { Rule } from '@helaba/iso20022-lib/rules';

/**
 * Allows storing errors for multiple FormGroups and making them available outside of the form context.
 */
@Injectable({
  providedIn: 'root',
})
export class FormErrorsService {
  #fieldErrors = signal<Map<string, string[]>>(new Map());
  #errorMessages = signal<Map<string, string>>(new Map());

  fieldErrors = this.#fieldErrors.asReadonly();
  errorMessages = this.#errorMessages.asReadonly();

  erroneousScopes = computed<Set<string>>(() => {
    const erroneousFields = Array.from(this.#fieldErrors().entries())
      .filter(([_, erroneousRules]) => erroneousRules.length > 0)
      .map(([fieldName]) => fieldName);

    const scopes = new Set<string>();
    for (const field of erroneousFields) {
      const parts = field.split('-');
      let prefix = '';

      for (const part of parts) {
        prefix = prefix ? `${prefix}-${part}` : part;
        scopes.add(prefix);
      }
    }

    return scopes;
  });

  /**
   * Update errors for a specific field
   */
  updateFieldErrors(fieldName: string, erroneousRules: string[] | null) {
    const currentErrors = new Map(this.#fieldErrors());

    if (!erroneousRules || erroneousRules.length === 0) {
      currentErrors.set(fieldName, []);
    } else {
      currentErrors.set(fieldName, erroneousRules);
    }

    this.#fieldErrors.set(currentErrors);
  }

  setErrorMessages(errorMessages: Map<string, string>) {
    this.#errorMessages.set(errorMessages);
  }
}
