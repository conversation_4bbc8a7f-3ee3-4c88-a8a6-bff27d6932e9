/**
 * Splits a PascalCase string into an array of words.
 * @param str the string to split.
 * @returns the individual parts.
 * @example
 * splitPascalCase("GroupHeader") // ["Group", "Header"]
 * splitPascalCase("FIToFICustomerCreditTransferV08") // ["FI", "To", "FI", "Customer", "Credit", "Transfer", "V08"]
 * splitPascalCase("CreditTransferTransactionInformation") // ["Credit", "Transfer", "Transaction", "Information"]
 * splitPascalCase("PreviousInstructingAgent1Account") // ["Previous", "Instructing", "Agent1", "Account"]
 * splitPascalCase("IBAN") // ["IBAN"]
 */
export function splitPascalCase(str: string): string[] {
  const result: string[] = [];
  let word = '';

  for (let i = 0; i < str.length; i++) {
    const char = str[i];
    const prev = str[i - 1];
    const next = str[i + 1];

    const isUpper = /[A-Z]/.test(char);
    const isPrevLowerAlphaNum =
      typeof prev !== 'undefined' && /[a-z0-9]/.test(prev);
    const isNextLowerAlphaNum =
      typeof next !== 'undefined' && /[a-z0-9]/.test(next);

    if (i > 0 && isUpper && (isPrevLowerAlphaNum || isNextLowerAlphaNum)) {
      result.push(word);
      word = char;
    } else {
      word += char;
    }
  }

  if (word) result.push(word);

  return result.filter((word) => !!word);
}

export function isPresent(value: unknown): boolean {
  return value !== null && value !== undefined && Array.isArray(value)
    ? value.length > 0 &&
        value.some(
          (entry) => entry !== null && entry !== undefined && entry !== ''
        )
    : value !== '';
}
