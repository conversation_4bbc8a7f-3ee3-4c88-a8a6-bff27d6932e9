import {
  AbstractControl,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import {
  Condition,
  ConditionalRule,
  isCondition,
  Rule,
} from '@helaba/iso20022-lib/rules';
import { isPresent } from '@helaba/iso20022-lib/util';

/**
 * Validates that the control's value is present (not null, undefined, or empty).
 * If the value is an array, it checks that at least one entry is present.
 * @param control the control to validate
 * @returns null if the value is present, or an object '{required: true}' if it is not present
 */
function requiredValidator(control: AbstractControl): ValidationErrors | null {
  return isPresent(control.value) ? null : { required: true };
}

/**
 * Validates that the control's value is not present.
 * If the value is an array, it checks that the array is empty or only has empty values.
 * @param control the control to validate
 * @returns null if the value is not present, or an object '{prohibitedValue: value}' if it is present
 */
function prohibitedValidator(
  control: AbstractControl
): ValidationErrors | null {
  return isPresent(control.value) ? { prohibitedValue: control.value } : null;
}

/**
 * Validates that the value of the control does not exceed the specified maximum length.
 * If the value is an array, it checks the length of each entry and returns the longest entry's length.
 * @param control the control to validate
 * @param maxLength the maximum allowed length for the string or array entries
 * @returns null if the value is within the maximum length, or an object '{maxlength: {requiredLength: 10, actualLength: 12}}' if it exceeds the maximum length
 */
function maxStringLengthValidator(
  control: AbstractControl,
  maxLength: number
): ValidationErrors | null {
  const value = control.value;
  if (!isPresent(value)) {
    return null;
  }

  if (Array.isArray(value)) {
    const longestEntryLength = value.reduce(
      (max, entry) => Math.max(max, entry?.length || 0),
      0
    );
    return longestEntryLength <= maxLength
      ? null
      : {
          maxlength: {
            requiredLength: maxLength,
            actualLength: longestEntryLength,
          },
        };
  }

  return value.length <= maxLength
    ? null
    : { maxlength: { requiredLength: maxLength, actualLength: value.length } };
}

/**
 * Checks if the value of the control matches the given pattern.
 * If the value is an array, it checks each entry against the pattern.
 * @param control the control to validate
 * @param pattern the regex pattern to match against
 * @returns null if the value matches the pattern, or an object '{pattern: {requiredPattern: '^[a-zA-Z ]*$', actualValue: '1'}}' if it does not match
 */
function patternValidator(
  control: AbstractControl,
  pattern: string
): ValidationErrors | null {
  const value = control.value;
  if (!isPresent(value)) {
    return null;
  }

  if (Array.isArray(value)) {
    const invalidEntries = value.filter(
      (entry) => !new RegExp(pattern).test(entry)
    );
    return invalidEntries.length > 0
      ? {
          pattern: {
            requiredPattern: pattern,
            actualValue: invalidEntries.join(', '),
          },
        }
      : null;
  }

  return value.match(new RegExp(pattern))
    ? null
    : {
        pattern: {
          requiredPattern: pattern,
          actualValue: value,
        },
      };
}

/**
 * Validates that the control's value does or does not contain the values of the 'otherFields'.
 * This is mostly meant for "the target field must not contain the value of another field" validation.
 * Nonetheless, we provide logic for the 'contains: true' case as well: At least one of the 'otherFields' values must be present in at least one of the target field's values.
 * @param control the control to validate
 * @param otherFields an array of field names whose values should be checked against the control's value
 * @param contains if true, the control's value must contain at least one of the 'otherFields' values; if false, the control's value must not contain any of the 'otherFields' values
 * @returns null if the validation passes, or an object '{contains: value}' if the control's value wrongly contains any of the 'otherFields' values, or '{notContains: otherValues}' if it wrongly does not contain any of the 'otherFields' values
 */
function containsValidator(
  control: AbstractControl,
  otherFields: string[],
  contains: boolean
): ValidationErrors | null {
  const controlValues = control.value;
  const values = !isPresent(controlValues)
    ? []
    : Array.isArray(controlValues)
    ? controlValues.filter(isPresent)
    : [controlValues];

  // 'flatMap' because the value of another field can be an array of strings
  const otherValues = otherFields.flatMap(
    (field) => control.root.get(field)?.value
  );

  if (!contains) {
    if (!isPresent(values)) {
      return null;
    }

    for (const value of values) {
      // 'value' might e.g. be an address line like "Street 13, 12345 City". We need to check against all parts of the value getting rid of spaces and commas.
      const valueParts = value.split(/[\s,]+/).filter(Boolean);
      if (valueParts.some((part: string) => otherValues.includes(part))) {
        return { contains: value };
      }
    }

    return null;
  } else {
    if (!isPresent(otherValues)) {
      return null;
    }

    for (const value of values) {
      // 'value' might e.g. be an address line like "Street 13, 12345 City". We need to check against all parts of the value getting rid of spaces and commas.
      const valueParts = value.split(/[\s,]+/).filter(Boolean);
      if (valueParts.some((part: string) => otherValues.includes(part))) {
        return null;
      }
    }

    return { notContains: otherValues.join(', ') };
  }
}

export function getBasicRuleErrors(
  control: AbstractControl,
  ruleToApply: Rule<string, undefined>
): ValidationErrors | null {
  switch (ruleToApply.type) {
    case 'required':
      if (ruleToApply.value === false) {
        return null;
      }
      return requiredValidator(control);
    case 'prohibited':
      if (ruleToApply.value === false) {
        return null;
      }
      return prohibitedValidator(control);
    case 'maxLength':
      return maxStringLengthValidator(control, ruleToApply.value);
    case 'maxItems':
      return Validators.maxLength(ruleToApply.value)(control);
    case 'pattern':
      return patternValidator(control, ruleToApply.value);
    case 'value':
      if (ruleToApply.isEqual === true) {
        return patternValidator(control, `^${ruleToApply.value}$`);
      }
      return patternValidator(control, `^(?!${ruleToApply.value}$).*`);
    case 'contains':
      return containsValidator(
        control,
        ruleToApply.value,
        ruleToApply.contains
      );
  }
  return null;
}

export function conditionValidator(
  fieldName: string,
  control: AbstractControl,
  conditionalRule: ConditionalRule<string, undefined>
): Record<string, ValidationErrors | null> | null {
  const formControls = control.parent;
  if (!formControls) {
    console.warn(
      'conditionValidator: No parent form controls found for the control.',
      control
    );
    return null;
  }
  const conditionsConnector = conditionalRule.conditionsConnector || 'and';
  const conditionsMet: boolean[] = [];

  for (const condition of conditionalRule.conditions) {
    if (isCondition(condition)) {
      const conditionIsMet = isConditionMet(condition, formControls);
      if (conditionsConnector === 'and' && !conditionIsMet) {
        // we don't have to check every condition, since all must be met
        return null;
      }
      conditionsMet.push(isConditionMet(condition, formControls));
    } else {
      const nestedConditionsMet: boolean[] = [];

      for (const nestedCondition of condition.conditions) {
        nestedConditionsMet.push(isConditionMet(nestedCondition, formControls));
      }

      const nestedConditionsConnector = condition.conditionsConnector || 'and';

      conditionsMet.push(
        nestedConditionsConnector === 'and'
          ? nestedConditionsMet.every(Boolean)
          : nestedConditionsMet.some(Boolean)
      );
    }
  }

  const allConditionsMet =
    conditionsConnector === 'and'
      ? conditionsMet.every(Boolean)
      : conditionsMet.some(Boolean);

  if (allConditionsMet) {
    const rulesConnector = conditionalRule.rulesConnector || 'and';
    const allErrors: Record<string, ValidationErrors | null> = {};
    const fieldErrors: Record<string, ValidationErrors | null> = {};
    for (const rule of conditionalRule.rules) {
      const relevantControl = formControls.get(rule.target);
      if (!relevantControl) {
        console.warn(
          `conditionValidator: Control for rule target "${rule.target}" not found in the form.`
        );
        continue;
      }
      const ruleErrors = getBasicRuleErrors(relevantControl, rule);
      allErrors[rule.id] = ruleErrors;
      if (rule.target === fieldName) {
        fieldErrors[rule.id] = ruleErrors; // We only want to return errors that target the current field
      }
    }

    const hasRulesMet = Object.values(allErrors).some(
      (error) => error === null
    );

    if (rulesConnector === 'or' && hasRulesMet) {
      // If it is sufficient that at least one rule is met, we don't want to show errors for the remaining rules, thus we just return null.
      return null;
    }

    return Object.fromEntries(
      Object.entries(fieldErrors).filter(([, value]) => value !== null)
    );
  }

  return null;
}

function isConditionMet(
  condition: Condition,
  formControls: AbstractControl
): boolean {
  const relatedControl = formControls.get(condition.field);
  if (!relatedControl) {
    return false; // If the related control is not found, we cannot evaluate the condition
  }
  const relatedValue = relatedControl.value;
  let conditionMet = false;

  switch (condition.type) {
    case 'value':
      conditionMet = Array.isArray(relatedValue)
        ? relatedValue.some((entry) => entry === condition.value)
        : relatedValue === condition.value;
      break;
    case 'present':
      conditionMet = isPresent(relatedValue);
      break;
    case 'notEqual':
      const otherField = formControls.get(condition.otherField);
      conditionMet = relatedValue !== otherField?.value;
  }

  return conditionMet;
}
