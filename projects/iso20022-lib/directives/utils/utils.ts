export function getChangedFields(
  previousValues: any,
  currentValues: any
): string[] {
  const changedFields: string[] = [];

  for (const field in currentValues) {
    const previousValue = previousValues[field];
    const currentValue = currentValues[field];

    if (Array.isArray(currentValue)) {
      // If the field is an array, we need to check if the array has changed
      if (
        !Array.isArray(previousValue) ||
        currentValue.length !== previousValue.length ||
        !currentValue.every((value, index) => value === previousValue[index])
      ) {
        changedFields.push(field);
      }
    } else {
      // For non-array fields, we check if the value has changed
      if (currentValue !== previousValue) {
        changedFields.push(field);
      }
    }
  }

  return changedFields;
}
