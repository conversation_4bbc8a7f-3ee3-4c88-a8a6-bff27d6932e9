import { Rule } from './types';
import rules from './generated/client-rules.json';
import affectedFieldsMap from './generated/affected-fields-for-field-value-change.json';
import expandedFormSetup from './generated/expanded-form-setup.json';

export const clientRules: Rule[] = rules;
export const pacs008AffectedFields: Record<string, string[]> =
  affectedFieldsMap;
export const formSetup = expandedFormSetup;
