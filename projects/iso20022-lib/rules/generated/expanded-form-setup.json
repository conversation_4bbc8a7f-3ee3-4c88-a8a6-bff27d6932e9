{"paymentBasics": {"visible": {"FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-InstrId": "InstructionIdentification", "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-EndToEndId": "EndToEndIdentification", "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-TxId": "TransactionIdentification", "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-InstrPrty": "InstructionPriority", "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-ClrChanl": "ClearingChannel", "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-Purp-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-Purp-Prtry": "Proprietary"}, "serverOnly": {"FIToFICstmrCdtTrf-GrpHdr-MsgId": "MessageIdentification", "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-UETR": "UETR", "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-ClrSysRef": "ClearingSystemReference", "FIToFICstmrCdtTrf-GrpHdr-CreDtTm": "CreationDateTime", "FIToFICstmrCdtTrf-GrpHdr-NbOfTxs": "NumberOfTransactions"}}, "amountCurrency": {"visible": {"FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAmt-amount": "amount", "FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-amount": "amount", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-XchgRate": "ExchangeRate", "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgBr": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-BICFI": "BICFI", "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-LEI": "LEI", "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Dept": "Department", "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-SubDept": "SubDepartment", "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-StrtNm": "StreetName", "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNm": "BuildingName", "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Flr": "Floor", "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstBx": "PostBox", "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Room": "Room", "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstCd": "PostCode", "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnNm": "TownName", "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Ctry": "Country", "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-AdrLine": "AddressLine", "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt-amount": "amount", "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt-Ccy": "<PERSON><PERSON><PERSON><PERSON>"}, "serverOnly": {}}, "settlementInformation": {"visible": {"FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmMtd": "SettlementMethod", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmDt": "InterbankSettlementDate", "FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmPrty": "SettlementPriority", "FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmIndctn-DbtDtTm": "DebitDateTime", "FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmIndctn-CdtDtTm": "CreditDateTime", "FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-FrTm": "FromTime", "FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-TillTm": "TillTime", "FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-CLSTm": "CLSTime", "FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-RjctTm": "RejectTime", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI": "BICFI", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-LEI": "LEI", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm": "Name", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept": "Department", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept": "SubDepartment", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm": "StreetName", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm": "BuildingName", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr": "Floor", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx": "PostBox", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room": "Room", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd": "PostCode", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm": "TownName", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry": "Country", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine": "AddressLine", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-IBAN": "IBAN", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Id": "Identification", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Tp-Cd": "Code", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Nm": "Name", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp-Cd": "Code", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Id": "Identification", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI": "BICFI", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-LEI": "LEI", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm": "Name", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept": "Department", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept": "SubDepartment", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm": "StreetName", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm": "BuildingName", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr": "Floor", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx": "PostBox", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room": "Room", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd": "PostCode", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm": "TownName", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry": "Country", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine": "AddressLine", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-IBAN": "IBAN", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Id": "Identification", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Tp-Cd": "Code", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Nm": "Name", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp-Cd": "Code", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Id": "Identification", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI": "BICFI", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-LEI": "LEI", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm": "Name", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept": "Department", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept": "SubDepartment", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm": "StreetName", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm": "BuildingName", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr": "Floor", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx": "PostBox", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room": "Room", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd": "PostCode", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm": "TownName", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry": "Country", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine": "AddressLine", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-IBAN": "IBAN", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Id": "Identification", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Tp-Cd": "Code", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Nm": "Name", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp-Cd": "Code", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Id": "Identification", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-IBAN": "IBAN", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id": "Identification", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Cd": "Code", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Nm": "Name", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Cd": "Code", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id": "Identification"}, "serverOnly": {}}, "debtorInformation": {"visible": {"FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Dept": "Department", "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-SubDept": "SubDepartment", "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-StrtNm": "StreetName", "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-BldgNb": "BuildingNumber", "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-BldgNm": "BuildingName", "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Flr": "Floor", "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-PstBx": "PostBox", "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Room": "Room", "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-PstCd": "PostCode", "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-TwnNm": "TownName", "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-TwnLctnNm": "TownLocationName", "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-DstrctNm": "DistrictName", "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-CtrySubDvsn": "CountrySubDivision", "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Ctry": "Country", "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-AdrLine": "AddressLine", "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-CtryOfRes": "CountryOfResidence", "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-AnyBIC": "AnyBIC", "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-LEI": "LEI", "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-IBAN": "IBAN", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Dept": "Department", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-SubDept": "SubDepartment", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-StrtNm": "StreetName", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-BldgNb": "BuildingNumber", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-BldgNm": "BuildingName", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Flr": "Floor", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-PstBx": "PostBox", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Room": "Room", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-PstCd": "PostCode", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-TwnNm": "TownName", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-TwnLctnNm": "TownLocationName", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-DstrctNm": "DistrictName", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-CtrySubDvsn": "CountrySubDivision", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Ctry": "Country", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-AdrLine": "AddressLine", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-CtryOfRes": "CountryOfResidence", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-AnyBIC": "AnyBIC", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-LEI": "LEI", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-Issr": "Issuer"}, "serverOnly": {}}, "creditorInformation": {"visible": {"FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Dept": "Department", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-SubDept": "SubDepartment", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-StrtNm": "StreetName", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-BldgNb": "BuildingNumber", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-BldgNm": "BuildingName", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Flr": "Floor", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-PstBx": "PostBox", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Room": "Room", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-PstCd": "PostCode", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-TwnNm": "TownName", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-TwnLctnNm": "TownLocationName", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-DstrctNm": "DistrictName", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-CtrySubDvsn": "CountrySubDivision", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Ctry": "Country", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-AdrLine": "AddressLine", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-CtryOfRes": "CountryOfResidence", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-AnyBIC": "AnyBIC", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-LEI": "LEI", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-IBAN": "IBAN", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Dept": "Department", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-SubDept": "SubDepartment", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-StrtNm": "StreetName", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-BldgNb": "BuildingNumber", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-BldgNm": "BuildingName", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Flr": "Floor", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-PstBx": "PostBox", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Room": "Room", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-PstCd": "PostCode", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-TwnNm": "TownName", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-TwnLctnNm": "TownLocationName", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-DstrctNm": "DistrictName", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-CtrySubDvsn": "CountrySubDivision", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Ctry": "Country", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-AdrLine": "AddressLine", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-CtryOfRes": "CountryOfResidence", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-AnyBIC": "AnyBIC", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-LEI": "LEI", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-Issr": "Issuer"}, "serverOnly": {}}, "financialInstitutionInformation": {"visible": {"FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Dept": "Department", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-SubDept": "SubDepartment", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-StrtNm": "StreetName", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-BldgNb": "BuildingNumber", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-BldgNm": "BuildingName", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Flr": "Floor", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-PstBx": "PostBox", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Room": "Room", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-PstCd": "PostCode", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-TwnNm": "TownName", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-TwnLctnNm": "TownLocationName", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-DstrctNm": "DistrictName", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-CtrySubDvsn": "CountrySubDivision", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Ctry": "Country", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-AdrLine": "AddressLine", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-CtryOfRes": "CountryOfResidence", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-AnyBIC": "AnyBIC", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-LEI": "LEI", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-BICFI": "BICFI", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-LEI": "LEI", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Dept": "Department", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-SubDept": "SubDepartment", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-StrtNm": "StreetName", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNm": "BuildingName", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Flr": "Floor", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstBx": "PostBox", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Room": "Room", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstCd": "PostCode", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnNm": "TownName", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Ctry": "Country", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine": "AddressLine", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-IBAN": "IBAN", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-BICFI": "BICFI", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-LEI": "LEI", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Dept": "Department", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-SubDept": "SubDepartment", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-StrtNm": "StreetName", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNm": "BuildingName", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Flr": "Floor", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstBx": "PostBox", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Room": "Room", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstCd": "PostCode", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm": "TownName", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry": "Country", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine": "AddressLine", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-BrnchId-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-IBAN": "IBAN", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-BICFI": "BICFI", "FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-LEI": "LEI", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-BICFI": "BICFI", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-LEI": "LEI", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Dept": "Department", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-SubDept": "SubDepartment", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-StrtNm": "StreetName", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNm": "BuildingName", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Flr": "Floor", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstBx": "PostBox", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Room": "Room", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstCd": "PostCode", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnNm": "TownName", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Ctry": "Country", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine": "AddressLine", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-IBAN": "IBAN", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-BICFI": "BICFI", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-LEI": "LEI", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Dept": "Department", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-SubDept": "SubDepartment", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-StrtNm": "StreetName", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNm": "BuildingName", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Flr": "Floor", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstBx": "PostBox", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Room": "Room", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstCd": "PostCode", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnNm": "TownName", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Ctry": "Country", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine": "AddressLine", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-IBAN": "IBAN", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-BICFI": "BICFI", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-LEI": "LEI", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Dept": "Department", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-SubDept": "SubDepartment", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-StrtNm": "StreetName", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNm": "BuildingName", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Flr": "Floor", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstBx": "PostBox", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Room": "Room", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstCd": "PostCode", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnNm": "TownName", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Ctry": "Country", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine": "AddressLine", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-IBAN": "IBAN", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-BICFI": "BICFI", "FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-LEI": "LEI", "FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-InstrInf": "InstructionInformation", "FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForNxtAgt-InstrInf": "InstructionInformation", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-BICFI": "BICFI", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-LEI": "LEI", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Dept": "Department", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-SubDept": "SubDepartment", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-StrtNm": "StreetName", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNm": "BuildingName", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Flr": "Floor", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstBx": "PostBox", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Room": "Room", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstCd": "PostCode", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnNm": "TownName", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Ctry": "Country", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine": "AddressLine", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-IBAN": "IBAN", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-BICFI": "BICFI", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-LEI": "LEI", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Dept": "Department", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-SubDept": "SubDepartment", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-StrtNm": "StreetName", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNm": "BuildingName", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Flr": "Floor", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstBx": "PostBox", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Room": "Room", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstCd": "PostCode", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnNm": "TownName", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Ctry": "Country", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine": "AddressLine", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-IBAN": "IBAN", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-BICFI": "BICFI", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId": "MemberIdentification", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-LEI": "LEI", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Dept": "Department", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-SubDept": "SubDepartment", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-StrtNm": "StreetName", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNb": "BuildingNumber", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNm": "BuildingName", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Flr": "Floor", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstBx": "PostBox", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Room": "Room", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstCd": "PostCode", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnNm": "TownName", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnLctnNm": "TownLocationName", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-DstrctNm": "DistrictName", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-CtrySubDvsn": "CountrySubDivision", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Ctry": "Country", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine": "AddressLine", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-IBAN": "IBAN", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id": "Identification"}, "serverOnly": {}}, "remittanceInformation": {"visible": {"FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Ustrd": "Unstructured", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Nb": "Number", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-RltdDt": "RelatedDate", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Nb": "Number", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-RltdDt": "RelatedDate", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Desc": "Description", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-amount": "amount", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-amount": "amount", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-amount": "amount", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-amount": "amount", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-amount": "amount", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-CdtDbtInd": "CreditDebitIndicator", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Rsn": "Reason", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-AddtlInf": "AdditionalInformation", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-amount": "amount", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DuePyblAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DuePyblAmt-amount": "amount", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-amount": "amount", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt-amount": "amount", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-amount": "amount", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-amount": "amount", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-CdtDbtInd": "CreditDebitIndicator", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Rsn": "Reason", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-AddtlInf": "AdditionalInformation", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-RmtdAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-RmtdAmt-amount": "amount", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Ref": "Reference", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Dept": "Department", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-SubDept": "SubDepartment", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-StrtNm": "StreetName", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-BldgNb": "BuildingNumber", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-BldgNm": "BuildingName", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Flr": "Floor", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-PstBx": "PostBox", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Room": "Room", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-PstCd": "PostCode", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-TwnNm": "TownName", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-TwnLctnNm": "TownLocationName", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-DstrctNm": "DistrictName", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-CtrySubDvsn": "CountrySubDivision", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Ctry": "Country", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-AdrLine": "AddressLine", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-CtryOfRes": "CountryOfResidence", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-AnyBIC": "AnyBIC", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-LEI": "LEI", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Dept": "Department", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-SubDept": "SubDepartment", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-StrtNm": "StreetName", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-BldgNb": "BuildingNumber", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-BldgNm": "BuildingName", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Flr": "Floor", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-PstBx": "PostBox", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Room": "Room", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-PstCd": "PostCode", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-TwnNm": "TownName", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-TwnLctnNm": "TownLocationName", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-DstrctNm": "DistrictName", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-CtrySubDvsn": "CountrySubDivision", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Ctry": "Country", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-AdrLine": "AddressLine", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-CtryOfRes": "CountryOfResidence", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-AnyBIC": "AnyBIC", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-LEI": "LEI", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-TaxId": "TaxIdentification", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-RegnId": "RegistrationIdentification", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-TaxTp": "TaxType", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-TaxId": "TaxIdentification", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-RegnId": "RegistrationIdentification", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-TaxTp": "TaxType", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Titl": "Title", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxId": "TaxIdentification", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-RegnId": "RegistrationIdentification", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxTp": "TaxType", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Titl": "Title", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-AdmstnZone": "AdministrationZone", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-RefNb": "ReferenceNumber", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Mtd": "Method", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-amount": "amount", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt-amount": "amount", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dt": "Date", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-SeqNb": "SequenceNumber", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Tp": "Type", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Ctgy": "Category", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-CtgyDtls": "CategoryDetails", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-DbtrSts": "DebtorS<PERSON>us", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-CertId": "CertificateIdentification", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-FrmsCd": "FormsCode", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-Yr": "Year", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-Tp": "Type", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-FrDt": "FromDate", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-ToDt": "ToDate", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Rate": "Rate", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-amount": "amount", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-amount": "amount", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-Yr": "Year", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-Tp": "Type", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-FrDt": "FromDate", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-ToDt": "ToDate", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-amount": "amount", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-AddtlInf": "AdditionalInformation", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Dept": "Department", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-SubDept": "SubDepartment", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-StrtNm": "StreetName", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-BldgNb": "BuildingNumber", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-BldgNm": "BuildingName", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Flr": "Floor", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-PstBx": "PostBox", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Room": "Room", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-PstCd": "PostCode", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-TwnNm": "TownName", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-TwnLctnNm": "TownLocationName", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-DstrctNm": "DistrictName", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-CtrySubDvsn": "CountrySubDivision", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Ctry": "Country", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-AdrLine": "AddressLine", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-CtryOfRes": "CountryOfResidence", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-AnyBIC": "AnyBIC", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-LEI": "LEI", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Dept": "Department", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-SubDept": "SubDepartment", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-StrtNm": "StreetName", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-BldgNb": "BuildingNumber", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-BldgNm": "BuildingName", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Flr": "Floor", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-PstBx": "PostBox", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Room": "Room", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-PstCd": "PostCode", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-TwnNm": "TownName", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-TwnLctnNm": "TownLocationName", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-DstrctNm": "DistrictName", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-CtrySubDvsn": "CountrySubDivision", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Ctry": "Country", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-AdrLine": "AddressLine", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-CtryOfRes": "CountryOfResidence", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-AnyBIC": "AnyBIC", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-LEI": "LEI", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-BirthDt": "BirthDate", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth": "ProvinceOfBirth", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth": "CityOfBirth", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth": "CountryOfBirth", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-Id": "Identification", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-SchmeNm-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-SchmeNm-Prtry": "Proprietary", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-Issr": "Issuer", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RefNb": "ReferenceNumber", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Dt": "Date", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RmtdAmt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RmtdAmt-amount": "amount", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-FmlyMdclInsrncInd": "FamilyMedicalInsuranceIndicator", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-MplyeeTermntnInd": "EmployeeTerminationIndicator", "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-AddtlRmtInf": "AdditionalRemittanceInformation", "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtId": "RemittanceIdentification", "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-Mtd": "Method", "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-ElctrncAdr": "ElectronicAddress", "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Dept": "Department", "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-SubDept": "SubDepartment", "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-StrtNm": "StreetName", "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-BldgNb": "BuildingNumber", "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-BldgNm": "BuildingName", "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Flr": "Floor", "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-PstBx": "PostBox", "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Room": "Room", "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-PstCd": "PostCode", "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-TwnNm": "TownName", "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-TwnLctnNm": "TownLocationName", "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-DstrctNm": "DistrictName", "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-CtrySubDvsn": "CountrySubDivision", "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Ctry": "Country", "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-AdrLine": "AddressLine"}, "serverOnly": {}}, "regulatoryReporting": {"visible": {"FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-DbtCdtRptgInd": "DebitCreditReportingIndicator", "FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Authrty-Nm": "Name", "FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Authrty-Ctry": "Country", "FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Tp": "Type", "FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Dt": "Date", "FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Ctry": "Country", "FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Cd": "Code", "FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Amt-amount": "amount", "FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Amt-Ccy": "<PERSON><PERSON><PERSON><PERSON>", "FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Inf": "Information"}, "serverOnly": {}}}