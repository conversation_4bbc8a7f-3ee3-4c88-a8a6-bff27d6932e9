const basicRuleTypes = [
  'required',
  'prohibited',
  'maxLength',
  'pattern',
  'value',
  'maxItems',
  'contains',
  'displayed',
  'serverOnly',
] as const;
export type BasicRuleType = (typeof basicRuleTypes)[number];

export type RuleType = BasicRuleType | 'condition';

type BaseRule<I = string, D = string> = {
  id: I;
  description: D;
  descriptionTranslationProposal: D;
  type: RuleType;
};

type BaseBasicRule<I = string, D = string> = BaseRule<I, D> & {
  target: string;
};

export type RequiredRule<I = string, D = string> = BaseBasicRule<I, D> & {
  type: 'required';
  value: boolean;
};

export type ProhibitedRule<I = string, D = string> = BaseBasicRule<I, D> & {
  type: 'prohibited';
  value: boolean;
};

type MaxLengthRule<I = string, D = string> = BaseBasicRule<I, D> & {
  type: 'maxLength';
  value: number;
};

export type PatternRule<I = string, D = string> = BaseBasicRule<I, D> & {
  type: 'pattern';
  value: string;
};

export type ValueRule<I = string, D = string> = BaseBasicRule<I, D> & {
  type: 'value';
  value: string;
  isEqual: boolean;
};

type MaxItemsRule<I = string, D = string> = BaseBasicRule<I, D> & {
  type: 'maxItems';
  value: number;
};

// E.g. "Data present in structured elements within the Postal Address must not be repeated in AddressLine" -> target: AdrLine, value: ['Dept', 'SubDept', 'StrtNm', ...], contains: false
type ContainsRule<I = string, D = string> = BaseBasicRule<I, D> & {
  type: 'contains';
  value: string[];
  contains: boolean;
};

type DisplayedRule<I = string, D = string> = BaseBasicRule<I, D> & {
  type: 'displayed';
  value: boolean;
};

export type ServerOnlyRule<I = string, D = string> = BaseBasicRule<I, D> & {
  type: 'serverOnly';
  value: boolean;
};

export type BasicRule<I = string, D = string> =
  | RequiredRule<I, D>
  | ProhibitedRule<I, D>
  | MaxLengthRule<I, D>
  | PatternRule<I, D>
  | ValueRule<I, D>
  | MaxItemsRule<I, D>
  | ContainsRule<I, D>
  | DisplayedRule<I, D>;

type BaseCondition = {
  field: string;
};

export type Condition = BaseCondition &
  (
    | {
        type: 'value';
        value: string;
      }
    | {
        type: 'present';
        value: boolean;
      }
    | {
        type: 'notEqual';
        otherField: string;
      }
  );

export type NestedCondition =
  | Condition
  | {
      conditions: Condition[]; // Only nested once.
      conditionsConnector?: 'and' | 'or';
    };

export function isCondition(cond: NestedCondition): cond is Condition {
  return 'type' in cond && 'field' in cond;
}

export function isNestedCondition(
  cond: NestedCondition
): cond is { conditions: Condition[]; conditionsConnector?: 'and' | 'or' } {
  return 'conditions' in cond;
}

export type ConditionsConnector = 'and' | 'or';

// The targets of conditional rules are in the rules array
export type ConditionalRule<I = string, D = string> = BaseRule<I, D> & {
  type: 'condition';
  conditions: NestedCondition[];
  conditionsConnector?: ConditionsConnector;
  rules: BasicRule<I, D>[];
  rulesConnector?: 'and' | 'or';
};

// Use D to communicate whether the description is required or not. It is not required for custom user defined rules, where we generate a description if none is provided. D is 'string' by default meaning the description is required.
export type Rule<I = string, D = string> =
  | BasicRule<I, D>
  | ConditionalRule<I, D>
  | ServerOnlyRule<I, D>;
