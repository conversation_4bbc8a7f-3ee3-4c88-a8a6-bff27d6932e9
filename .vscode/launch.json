{
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Rule Generation Script",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/rule-generation/scripts/main.ts",
      "args": [
        "rule-generation/input/pacs.008/pacs.008.001.08_cbprplus/schema.json",
        "rule-generation/input/pacs.008/pacs.008.001.08_cbprplus/schema.xsd",
        "rule-generation/input/pacs.008/form-setup.json",
        "--output-folder",
        "projects/iso20022-lib/rules/generated",
        "--base-element",
        "fi_to_fi_customer_credit_transfer_v08"
      ],
      "outFiles": ["${workspaceFolder}/rule-generation/scripts/js/**/*.js"],
      "sourceMaps": true,
      "preLaunchTask": "tsc: build - rule-generation/tsconfig.scripts.json",
      "cwd": "${workspaceFolder}",
      "internalConsoleOptions": "openOnSessionStart"
    }
  ]
}
