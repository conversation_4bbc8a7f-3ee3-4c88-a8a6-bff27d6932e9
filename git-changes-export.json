{"generatedAt": 1753976684106, "commits": [{"hash": "0d0705f1632d30c354e5f20325bb4d75df6828ab", "message": "update form setup", "timestamp": 1753976675000, "files": [{"path": "projects/test-app/src/app/app.component.html", "status": "M", "content": "<div>\n  <h2>Test Form</h2>\n\n  <form\n    [formGroup]=\"testFormGroup\"\n    [formRules]=\"rules\"\n    [affectedFields]=\"affectedFields\"\n    (ngSubmit)=\"onSubmit()\"\n  >\n    <div autohide=\"field1\">\n      <label for=\"field1\">Field 1</label>\n      <input id=\"field1\" type=\"text\" formControlName=\"field1\" />\n      <app-form-field-errors fieldName=\"field1\" />\n    </div>\n    <div autohide=\"field2\">\n      <label for=\"field2\">Field 2</label>\n      <input id=\"field2\" type=\"text\" formControlName=\"field2\" />\n      <app-form-field-errors fieldName=\"field2\" />\n    </div>\n    <app-one-of-selector\n      header=\"Select between field 3 and field 4\"\n      option1Label=\"Field 3\"\n      option2Label=\"Field 4\"\n      [option1Fields]=\"['field3']\"\n      [option2Fields]=\"['field4']\"\n      [isReadOnly]=\"false\"\n    >\n      <ng-container slot=\"option1\">\n        <div autohide=\"field3\">\n          <label for=\"field3\">Field 3</label>\n          <input id=\"field3\" type=\"text\" formControlName=\"field3\" />\n          <app-form-field-errors fieldName=\"field3\" />\n        </div>\n      </ng-container>\n      <ng-container slot=\"option2\">\n        <div autohide=\"field4\">\n          <label for=\"field4\">Field 4</label>\n          <input id=\"field4\" type=\"text\" formControlName=\"field4\" />\n          <app-form-field-errors fieldName=\"field4\" />\n        </div>\n      </ng-container>\n    </app-one-of-selector>\n    <div autohide=\"field5\">\n      <label for=\"field5\">Field 5</label>\n      <input id=\"field5\" type=\"text\" formControlName=\"field5\" />\n      <app-form-field-errors fieldName=\"field5\" />\n    </div>\n    <div autohide=\"field6\">\n      <label for=\"field6\">Field 6</label>\n      <input id=\"field6\" type=\"text\" formControlName=\"field6\" />\n      <app-form-field-errors fieldName=\"field6\" />\n    </div>\n    <div autohide=\"field7\">\n      <label for=\"field7\">Field 7</label>\n      <div formArrayName=\"field7\">\n        @for (entry of testFormGroup.controls.field7.controls; track index; let\n        index = $index;) {\n        <div>\n          <input type=\"text\" [formControlName]=\"index\" />\n          <span (click)=\"removeItem(index)\">X</span>\n        </div>\n        }\n      </div>\n      <app-form-field-errors fieldName=\"field7\" />\n      <button (click)=\"addItem()\">Add item</button>\n    </div>\n    <div autohide=\"field8\">\n      <label for=\"field8\">Field 8</label>\n      <input id=\"field8\" type=\"text\" formControlName=\"field8\" />\n      <app-form-field-errors fieldName=\"field8\" />\n    </div>\n    <div autohide=\"field9\">\n      <label for=\"field9\">Field 9</label>\n      <input id=\"field9\" type=\"text\" formControlName=\"field9\" />\n      <app-form-field-errors fieldName=\"field9\" />\n    </div>\n    <div autohide=\"field10\">\n      <label for=\"field10\">Field 10</label>\n      <input id=\"field10\" type=\"text\" formControlName=\"field10\" />\n      <app-form-field-errors fieldName=\"field10\" />\n    </div>\n\n    <div>\n      <button type=\"submit\">Submit</button>\n    </div>\n  </form>\n</div>"}, {"path": "projects/test-app/src/app/app.component.ts", "status": "M", "content": "import {\n  Component,\n  ChangeDetectionStrategy,\n  inject,\n  effect,\n} from '@angular/core';\nimport { NonNullableFormBuilder, ReactiveFormsModule } from '@angular/forms';\nimport {\n  FormRulesDirective,\n  AutohideDirective,\n} from '@helaba/iso20022-lib/directives';\nimport { Rule } from '@helaba/iso20022-lib/rules';\nimport { FormErrorsService } from '@helaba/iso20022-lib/error';\nimport {\n  affectedFields,\n  errorMessages,\n  testValidationRules,\n} from './test-validation-rules';\nimport { getFormGroup } from './app.form';\nimport { OneOfSelectorComponent } from './components/one-of-selector/one-of-selector.component';\nimport { FormFieldErrorsComponent } from './components/form-field-errors/form-field-errors.component';\n\n@Component({\n  selector: 'app-root',\n  imports: [\n    ReactiveFormsModule,\n    FormRulesDirective,\n    AutohideDirective,\n    OneOfSelectorComponent,\n    FormFieldErrorsComponent,\n  ],\n  templateUrl: './app.component.html',\n  styleUrl: './app.component.scss',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class AppComponent {\n  fb = inject(NonNullableFormBuilder);\n  formErrorsService = inject(FormErrorsService);\n\n  testFormGroup = getFormGroup(this.fb);\n  rules: Rule<string, undefined>[] = testValidationRules;\n  affectedFields = affectedFields;\n\n  constructor() {\n    effect(() => {\n      this.formErrorsService.setErrorMessages(errorMessages);\n    });\n  }\n\n  addItem() {\n    this.testFormGroup.controls.field7.push(this.fb.control(''));\n  }\n\n  removeItem(index: number) {\n    this.testFormGroup.controls.field7.removeAt(index);\n  }\n\n  onSubmit() {\n    for (const key in this.testFormGroup.controls) {\n      const control = this.testFormGroup.get(key);\n      control?.updateValueAndValidity();\n      control?.markAsTouched();\n    }\n    if (this.testFormGroup.valid) {\n      console.log('Submitting', this.testFormGroup.getRawValue());\n    }\n  }\n}"}, {"path": "projects/test-app/src/app/app.form.ts", "status": "A", "content": "import { FormControl, FormGroup, NonNullableFormBuilder } from '@angular/forms';\n\nexport function getFormSchema(fb: NonNullableFormBuilder) {\n  return {\n    field1: fb.control(''),\n    field2: fb.control(''),\n    field3: fb.control(''),\n    field4: fb.control(''),\n    field5: fb.control(''),\n    field6: fb.control(''),\n    field7: fb.array<FormControl<string>>([]),\n    field8: fb.control(''),\n    field9: fb.control(''),\n    field10: fb.control(''),\n    field11: fb.array<\n      FormGroup<{\n        nestedField1: FormControl<string>;\n        nestedField2: FormControl<string>;\n      }>\n    >([]),\n  };\n}\n\nexport type AppFormSchema = ReturnType<typeof getFormSchema>;\n\nexport function getFormGroup(fb: NonNullableFormBuilder) {\n  const schema = getFormSchema(fb);\n  return fb.group(schema);\n}"}, {"path": "projects/test-app/src/app/app.types.ts", "status": "D"}, {"path": "projects/test-app/src/app/components/base-field/base-field.component.html", "status": "M", "content": "<div class=\"form-field\">\n  @if (fieldNames().length > 1) {\n  <p-fieldset [legend]=\"label()\" class=\"form-field-fieldset\">\n    <div>\n      <ng-container *ngTemplateOutlet=\"projectedContent\"></ng-container>\n    </div>\n    @if (allowShowingErrors()) {\n    <div>\n      @for (fieldName of fieldNames(); track fieldName) {\n      <app-form-field-errors [fieldName]=\"fieldName\" />\n      }\n    </div>\n    }\n  </p-fieldset>\n  } @if (fieldNames().length === 1) { @if (useFieldset()) {\n  <app-plain-fieldset [legend]=\"label()\">\n    <div>\n      <ng-container *ngTemplateOutlet=\"projectedContent\"></ng-container>\n    </div>\n  </app-plain-fieldset>\n  } @else {\n  <label [for]=\"fieldId()\" class=\"form-field-label\">{{ label() }}</label>\n  }\n\n  <div>\n    <ng-container *ngTemplateOutlet=\"projectedContent\"></ng-container>\n  </div>\n  <div>\n    <app-form-field-errors [fieldName]=\"fieldNames()[0]\" />\n  </div>\n  }\n</div>\n\n<!-- There must only be one 'ng-content' in the template if we don't want to use slots which should not be necessary in this case as the two branches are mutually exclusive. -->\n<ng-template #projectedContent>\n  <ng-content></ng-content>\n</ng-template>"}, {"path": "projects/test-app/src/app/components/base-field/base-field.component.ts", "status": "M", "content": "import { NgTemplateOutlet } from '@angular/common';\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  computed,\n  inject,\n  input,\n} from '@angular/core';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { FormErrorsService } from '@helaba/iso20022-lib/error';\nimport { FieldsetModule } from 'primeng/fieldset';\nimport { FormFieldErrorsComponent } from '../form-field-errors';\nimport { PlainFieldsetComponent } from '../plain-fieldset';\n\n@Component({\n  selector: 'app-base-field',\n  imports: [\n    ReactiveFormsModule,\n    FieldsetModule,\n    NgTemplateOutlet,\n    FormFieldErrorsComponent,\n    PlainFieldsetComponent,\n  ],\n  templateUrl: './base-field.component.html',\n  styleUrl: './base-field.component.css',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class BaseFieldComponent {\n  label = input.required<string>();\n  fieldNames = input.required<string[]>(); // We allow multiple field names such that we can use this component for error display for OneOfSelector components while no option is selected. All children fields that only allow for a single field name should throw an error if more than one field name is provided.\n  fieldPrefix = input<string>(''); // Optional prefix for the field names, this needs to set for fields that are used inside a form group array template and allows the BaseField to set a unique field ID.\n  isReadOnly = input.required<boolean>();\n  allowShowingErrors = input<boolean>(true);\n  useFieldset = input<boolean>(false); // Use a fieldset with a legend instead of a label even for cases with only one field name. E.g. a radio button group has multiple inputs but only one field name which cannot be used in the \"for\" attribute of the label as it would refer to none of the inputs.\n\n  formErrorsService = inject(FormErrorsService);\n\n  protected hasError = computed<boolean>(() => {\n    if (this.isReadOnly()) {\n      return false;\n    }\n\n    const erroneousScopes = this.formErrorsService.erroneousScopes();\n\n    for (const fieldName of this.fieldNames()) {\n      if (erroneousScopes.has(fieldName)) {\n        return true;\n      }\n    }\n    return false;\n  });\n\n  protected fieldId = computed<string | undefined>(() => {\n    if (this.fieldNames().length > 1) {\n      return undefined;\n    }\n    const fieldName = this.fieldNames()[0];\n    return `${this.fieldPrefix()}${fieldName}`;\n  });\n}"}, {"path": "projects/test-app/src/app/components/error/error.component.css", "status": "D"}, {"path": "projects/test-app/src/app/components/error/error.component.html", "status": "D"}, {"path": "projects/test-app/src/app/components/error/error.component.ts", "status": "D"}, {"path": "projects/test-app/src/app/components/error/index.ts", "status": "D"}, {"path": "projects/test-app/src/app/components/form-field-errors/form-field-errors.component.css", "status": "A", "content": ".field-errors-container {\n  margin-top: 0.5rem;\n  margin-bottom: 0.5rem;\n\n  .field-errors-tag {\n    max-width: fit-content;\n  }\n}"}, {"path": "projects/test-app/src/app/components/form-field-errors/form-field-errors.component.html", "status": "A", "content": "<div class=\"field-errors-container\">\n  <field-errors [fieldName]=\"fieldName()\">\n    <ng-template let-errorMessage>\n      <p-tag\n        [value]=\"errorMessage\"\n        severity=\"danger\"\n        class=\"field-errors-tag\"\n      />\n    </ng-template>\n  </field-errors>\n</div>"}, {"path": "projects/test-app/src/app/components/form-field-errors/form-field-errors.component.ts", "status": "A", "content": "import { ChangeDetectionStrategy, Component, input } from '@angular/core';\nimport { FieldErrorsComponent } from '../../../../../iso20022-lib/error';\nimport { TagModule } from 'primeng/tag';\n\n@Component({\n  selector: 'app-form-field-errors',\n  imports: [TagModule, FieldErrorsComponent],\n  templateUrl: './form-field-errors.component.html',\n  styleUrl: './form-field-errors.component.css',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class FormFieldErrorsComponent {\n  fieldName = input.required<string>();\n}"}, {"path": "projects/test-app/src/app/components/form-field-errors/index.ts", "status": "A", "content": "export * from './form-field-errors.component';"}, {"path": "projects/test-app/src/app/components/index.ts", "status": "M", "content": "export * from './base-field';\nexport * from './one-of-selector';\nexport * from './form-field-errors';"}, {"path": "projects/test-app/src/app/components/one-of-selector/one-of-selector.component.html", "status": "M", "content": "<app-base-field\n  [label]=\"header()\"\n  [fieldNames]=\"allFields()\"\n  [allowShowingErrors]=\"showErrors()\"\n  [isReadOnly]=\"isReadOnly()\"\n>\n  <div class=\"one-of-select-button\">\n    <app-select-button\n      [options]=\"selectButtonOptions()\"\n      [defaultValue]=\"\n        isReadOnly()\n          ? hasFilledOutOption1Fields()\n            ? OPTION1\n            : hasFilledOutOption2Fields()\n            ? OPTION2\n            : null\n          : null\n      \"\n      [isReadOnly]=\"isReadOnly()\"\n      (onChange)=\"onChangeSelectedOption($event)\"\n    />\n  </div>\n  <div class=\"one-of-content\">\n    @if (selectedOption() === OPTION1) {\n    <p-panel>\n      <ng-content select=\"[slot='option1']\" />\n    </p-panel>\n    } @if (selectedOption() === OPTION2) {\n    <p-panel>\n      <ng-content select=\"[slot='option2']\" />\n    </p-panel>\n    } @if (isReadOnly() && !hasFilledOutOption1Fields() &&\n    !hasFilledOutOption2Fields()) {\n    <p-panel> No option selected. </p-panel>\n    }\n  </div>\n</app-base-field>"}, {"path": "projects/test-app/src/app/components/one-of-selector/one-of-selector.component.ts", "status": "M", "content": "import {\n  ChangeDetectionStrategy,\n  Component,\n  computed,\n  inject,\n  input,\n  signal,\n} from '@angular/core';\nimport { PanelModule } from 'primeng/panel';\nimport { ControlContainer, FormGroup } from '@angular/forms';\nimport { isPresent } from '@helaba/iso20022-lib/util';\nimport { BaseFieldComponent } from '../base-field';\nimport { SelectButtonComponent } from '../select-button';\nimport { SelectOption } from '../../types';\n\nconst OPTION1_VALUE = 'option1';\nconst OPTION2_VALUE = 'option2';\n\nfunction resetField(fieldName: string, formGroup: FormGroup) {\n  const fieldControl = formGroup.get(fieldName);\n  if (fieldControl) {\n    fieldControl.reset();\n    fieldControl.markAsTouched();\n  }\n}\n\n@Component({\n  selector: 'app-one-of-selector',\n  imports: [BaseFieldComponent, SelectButtonComponent, PanelModule],\n  templateUrl: './one-of-selector.component.html',\n  styleUrl: './one-of-selector.component.css',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class OneOfSelectorComponent {\n  readonly OPTION1 = OPTION1_VALUE;\n  readonly OPTION2 = OPTION2_VALUE;\n\n  header = input.required<string>();\n  option1Label = input.required<string>();\n  option2Label = input.required<string>();\n  option1Fields = input.required<string[]>();\n  option2Fields = input.required<string[]>();\n  isReadOnly = input.required<boolean>();\n\n  controlContainer = inject(ControlContainer);\n\n  get formGroup(): FormGroup {\n    return this.controlContainer.control as FormGroup;\n  }\n\n  allFields = computed<string[]>(() => {\n    return [...this.option1Fields(), ...this.option2Fields()];\n  });\n\n  selectButtonOptions = computed<SelectOption<string>[]>(() => {\n    return [\n      {\n        key: this.option1Label(),\n        label: this.option1Label(),\n        value: this.OPTION1,\n      },\n      {\n        key: this.option2Label(),\n        label: this.option2Label(),\n        value: this.OPTION2,\n      },\n    ];\n  });\n\n  selectedOption = signal<string | null>(null);\n\n  // Only show errors if no option is selected. If an option is selected, the individual fields are shown, displaying their own errors.\n  showErrors = computed(() => {\n    return this.selectedOption() === null || this.selectedOption() === '';\n  });\n\n  onChangeSelectedOption(newSelectedOption: string) {\n    this.selectedOption.set(newSelectedOption);\n\n    if (newSelectedOption === this.OPTION1) {\n      for (const field of this.option2Fields()) {\n        resetField(field, this.formGroup);\n      }\n    } else if (newSelectedOption === this.OPTION2) {\n      for (const field of this.option1Fields()) {\n        resetField(field, this.formGroup);\n      }\n    }\n  }\n\n  // Compute whether the user has filled out option 1 or option 2 fields (or none) for readonly display.\n  hasFilledOutOption1Fields = computed(() => {\n    return this.option1Fields().some((field) => {\n      const fieldControl = this.formGroup.get(field);\n      return isPresent(fieldControl?.value);\n    });\n  });\n\n  hasFilledOutOption2Fields = computed(() => {\n    return this.option2Fields().some((field) => {\n      const fieldControl = this.formGroup.get(field);\n      return isPresent(fieldControl?.value);\n    });\n  });\n}"}, {"path": "projects/test-app/src/app/components/plain-fieldset/index.ts", "status": "A", "content": "export * from './plain-fieldset.component';"}, {"path": "projects/test-app/src/app/components/plain-fieldset/plain-fieldset.component.html", "status": "A", "content": "<p-fieldset [legend]=\"legend()\" class=\"plainFieldset\">\n  <ng-content />\n</p-fieldset>"}, {"path": "projects/test-app/src/app/components/plain-fieldset/plain-fieldset.component.scss", "status": "A", "content": ".plainFieldset {\n  --p-fieldset-border-color: transparent;\n  --p-fieldset-background: transparent;\n  --p-fieldset-legend-background: transparent;\n  --p-fieldset-padding: 0;\n  --p-fieldset-legend-padding: 0;\n}"}, {"path": "projects/test-app/src/app/components/plain-fieldset/plain-fieldset.component.ts", "status": "A", "content": "import { ChangeDetectionStrategy, Component, input } from '@angular/core';\nimport { FieldsetModule } from 'primeng/fieldset';\n\n@Component({\n  selector: 'app-plain-fieldset',\n  imports: [FieldsetModule],\n  templateUrl: './plain-fieldset.component.html',\n  styleUrl: './plain-fieldset.component.scss',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class PlainFieldsetComponent {\n  legend = input.required<string>();\n}"}, {"path": "projects/test-app/src/app/components/select-button/select-button.component.html", "status": "M", "content": "<p-selectbutton\n  [options]=\"options()\"\n  [(ngModel)]=\"selectedOption\"\n  optionLabel=\"label\"\n  optionValue=\"value\"\n  size=\"small\"\n  [disabled]=\"isReadOnly()\"\n/>"}, {"path": "projects/test-app/src/app/components/select-button/select-button.component.ts", "status": "M", "content": "import {\n  ChangeDetectionStrategy,\n  Component,\n  effect,\n  input,\n  model,\n  output,\n} from '@angular/core';\nimport { FormsModule } from '@angular/forms';\nimport { SelectButtonModule } from 'primeng/selectbutton';\nimport { SelectOption } from '../../types';\n\n@Component({\n  selector: 'app-select-button',\n  imports: [SelectButtonModule, FormsModule],\n  templateUrl: './select-button.component.html',\n  styleUrl: './select-button.component.css',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class SelectButtonComponent<T> {\n  options = input.required<SelectOption<T>[]>();\n  defaultValue = input<T | null>(null);\n  isReadOnly = input.required<boolean>();\n  onChange = output<T | null>();\n\n  selectedOption = model<T | null>(null);\n\n  constructor() {\n    effect(() => {\n      this.selectedOption.set(this.defaultValue());\n    });\n    effect(() => {\n      this.onChange.emit(this.selectedOption());\n    });\n  }\n}"}, {"path": "projects/test-app/src/app/test-validation-rules.ts", "status": "M", "content": "import { isCondition, Rule } from '@helaba/iso20022-lib/rules';\n\nexport const testValidationRules: Rule<string, undefined>[] = [\n  {\n    id: 'rule1',\n    description: undefined,\n    descriptionTranslationProposal: undefined,\n    type: 'required',\n    target: 'field1',\n    value: true,\n  },\n  {\n    id: 'rule2',\n    description: undefined,\n    descriptionTranslationProposal: undefined,\n    type: 'prohibited',\n    target: 'field2',\n    value: true,\n  },\n  {\n    id: 'rule3',\n    description: undefined,\n    descriptionTranslationProposal: undefined,\n    type: 'required',\n    target: 'field3',\n    value: true,\n  },\n  {\n    id: 'rule3.2',\n    description: undefined,\n    descriptionTranslationProposal: undefined,\n    type: 'maxLength',\n    target: 'field3',\n    value: 5,\n  },\n  {\n    id: 'rule4',\n    description: undefined,\n    descriptionTranslationProposal: undefined,\n    type: 'pattern',\n    target: 'field4',\n    value: '^[a-zA-Z0-9]*$',\n  },\n  {\n    id: 'rule5',\n    description: undefined,\n    descriptionTranslationProposal: undefined,\n    type: 'value',\n    target: 'field5',\n    value: 'test',\n    isEqual: true,\n  },\n  {\n    id: 'rule6',\n    description: undefined,\n    descriptionTranslationProposal: undefined,\n    type: 'value',\n    target: 'field6',\n    value: 'test',\n    isEqual: false,\n  },\n  {\n    id: 'rule7',\n    description: undefined,\n    descriptionTranslationProposal: undefined,\n    type: 'maxItems',\n    target: 'field7',\n    value: 3,\n  },\n  {\n    id: 'rule8',\n    description: undefined,\n    descriptionTranslationProposal: undefined,\n    type: 'contains',\n    target: 'field8',\n    contains: true,\n    value: ['field5'],\n  },\n  {\n    id: 'rule9',\n    description: undefined,\n    descriptionTranslationProposal: undefined,\n    type: 'contains',\n    target: 'field8',\n    contains: false,\n    value: ['field6', 'field7'],\n  },\n  {\n    id: 'rule10',\n    description: undefined,\n    descriptionTranslationProposal: undefined,\n    type: 'condition',\n    conditions: [\n      {\n        field: 'field1',\n        type: 'value',\n        value: 'test',\n      },\n    ],\n    rules: [\n      {\n        id: 'rule10-field9-required',\n        description: undefined,\n        descriptionTranslationProposal: undefined,\n        type: 'required',\n        target: 'field9',\n        value: true,\n      },\n      {\n        id: 'rule10-field10-prohibited',\n        description: undefined,\n        descriptionTranslationProposal: undefined,\n        type: 'prohibited',\n        target: 'field10',\n        value: true,\n      },\n    ],\n  },\n  {\n    id: 'rule11',\n    description: undefined,\n    descriptionTranslationProposal: undefined,\n    type: 'condition',\n    conditions: [\n      {\n        field: 'field7',\n        type: 'present',\n        value: true,\n      },\n    ],\n    rules: [\n      {\n        id: 'rule11-field9-maxLength',\n        description: undefined,\n        descriptionTranslationProposal: undefined,\n        type: 'maxLength',\n        target: 'field9',\n        value: 3,\n      },\n      {\n        id: 'rule11-field10-value',\n        description: undefined,\n        descriptionTranslationProposal: undefined,\n        type: 'value',\n        target: 'field10',\n        value: 'test10',\n        isEqual: true,\n      },\n    ],\n  },\n  {\n    id: 'rule12',\n    description: undefined,\n    descriptionTranslationProposal: undefined,\n    type: 'maxItems',\n    target: 'field11',\n    value: 2,\n  },\n];\n\nexport const errorMessages: Map<string, string> = new Map(\n  Object.entries({\n    rule1: 'Field 1 is required',\n    rule2: 'Field 2 is prohibited',\n    rule3: 'Field 3 is required',\n    'rule3.2': 'Field 3 has a maximum length of 5 characters',\n    rule4: 'Field 4 should only contain alphanumeric characters',\n    rule5: \"Field 5 must have the value 'test'\",\n    rule6: \"Field 6 must not have the value 'test'\",\n    rule7: 'Field 7 should have a maximum of 3 items',\n    rule8: 'Field 8 must contain the value from Field 5',\n    rule9: 'Field 8 must not contain the value from Field 6 or Field 7',\n    'rule10-field9-required': \"Field 9 is required if Field 1 is 'test'\",\n    'rule10-field10-prohibited': \"Field 10 is prohibited if Field 1 is 'test'\",\n    'rule11-field9-maxLength':\n      'Field 9 has a maximum length of 3 if Field 7 is present',\n    'rule11-field10-value':\n      \"Field 10 must have the value 'test10' if Field 7 is present\",\n    rule12: 'Field 11 should have a maximum of 2 items',\n  })\n);\n\nconst affectedFieldsMap =\n  computeAffectedFieldsForFieldValueChange(testValidationRules);\n\nexport const affectedFields = mapToObject(affectedFieldsMap);\n\nfunction computeAffectedFieldsForFieldValueChange(\n  rules: Rule<string, undefined>[]\n): Map<string, Set<string>> {\n  const affectedFieldsForChange: Map<string, Set<string>> = new Map();\n\n  for (const rule of rules) {\n    if (rule.type === 'condition') {\n      // For conditional rules, if the changed field is part of a condition, all targets of subrules might be affected\n      const conditionTargets: string[] = rule.conditions.flatMap((condition) =>\n        isCondition(condition)\n          ? [condition.field]\n          : condition.conditions.map((c) => c.field)\n      );\n\n      const subRuleTargets: string[] = rule.rules.map(\n        (subRule) => subRule.target\n      );\n\n      for (const target of conditionTargets) {\n        if (affectedFieldsForChange.has(target)) {\n          const affectedFieldsForRule = affectedFieldsForChange.get(target);\n          for (const subRuleTarget of subRuleTargets) {\n            affectedFieldsForRule?.add(subRuleTarget);\n          }\n        } else {\n          affectedFieldsForChange.set(target, new Set(subRuleTargets));\n        }\n      }\n    } else {\n      if (affectedFieldsForChange.has(rule.target)) {\n        const affectedFieldsForRule = affectedFieldsForChange.get(rule.target);\n        affectedFieldsForRule?.add(rule.target);\n      } else {\n        affectedFieldsForChange.set(rule.target, new Set([rule.target]));\n      }\n    }\n\n    if (rule.type === 'contains') {\n      // For 'contains' rules, the target of the rule is also affected by changes to the 'otherField's of the rule.\n      const otherFields = rule.value;\n      for (const otherField of otherFields) {\n        if (affectedFieldsForChange.has(otherField)) {\n          const affectedFieldsForRule = affectedFieldsForChange.get(otherField);\n          affectedFieldsForRule?.add(rule.target);\n        } else {\n          affectedFieldsForChange.set(otherField, new Set([rule.target]));\n        }\n      }\n    }\n  }\n\n  return affectedFieldsForChange;\n}\n\nfunction mapToObject(map: Map<string, Set<string>>): Record<string, string[]> {\n  const obj: Record<string, string[]> = {};\n  for (const [key, valueSet] of map.entries()) {\n    obj[key] = Array.from(valueSet);\n  }\n\n  return obj;\n}"}, {"path": "rule-generation/scripts/generate-rules.ts", "status": "M", "content": "import {\n  Condition,\n  ConditionalRule,\n  PatternRule,\n  RequiredRule,\n  Rule,\n} from '../../projects/iso20022-lib/rules';\nimport { XsdElement } from './types';\nimport {\n  generateConditionalExplanation,\n  GENERATED_RULE_PREFIX,\n  generateRuleDescription,\n  generateRuleId,\n  getDefinition,\n  getNestedFieldNames,\n  getObjectProperties,\n  getRef,\n  getRequiredProperties,\n  isValidObject,\n} from './utils';\n\nfunction getRequiredRule(\n  nestedFieldName: string,\n  conditionalExplanation: string | undefined,\n  translatedConditionalExplanation: string | undefined,\n  conditionalParentRuleId: string | undefined,\n  nameMappings: XsdElement\n): RequiredRule {\n  return {\n    id: generateRuleId('required', GENERATED_RULE_PREFIX, nestedFieldName, {\n      isConditional:\n        !!conditionalExplanation && !!translatedConditionalExplanation,\n      conditionalParentRuleId,\n    }),\n    description: generateRuleDescription(\n      'required',\n      nestedFieldName,\n      nameMappings,\n      'en',\n      {\n        ruleDependentValue: true,\n        conditionalExplanation,\n      }\n    ),\n    descriptionTranslationProposal: generateRuleDescription(\n      'required',\n      nestedFieldName,\n      nameMappings,\n      'de',\n      {\n        ruleDependentValue: true,\n        conditionalExplanation: translatedConditionalExplanation,\n      }\n    ),\n    type: 'required',\n    value: true,\n    target: nestedFieldName,\n  };\n}\n\nfunction getPatternRule(\n  nestedFieldName: string,\n  regex: string | undefined,\n  enumValues: string[] | undefined,\n  conditionalExplanation: string | undefined,\n  translatedConditionalExplanation: string | undefined,\n  conditionalParentRuleId: string | undefined,\n  nameMappings: XsdElement\n): PatternRule {\n  // Escape special regex characters in the strings.\n  const escapedEnumValues = enumValues?.map((value) =>\n    value.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&')\n  );\n  const joinedEnumValues = escapedEnumValues\n    ? `^(${escapedEnumValues.join('|')})?$`\n    : undefined;\n  const pattern = regex || joinedEnumValues;\n\n  if (!pattern) {\n    throw new Error(\n      `Invalid JSON schema: '${nestedFieldName}' has no valid pattern or enum values.`\n    );\n  }\n\n  return {\n    id: generateRuleId('pattern', GENERATED_RULE_PREFIX, nestedFieldName, {\n      isConditional: !!conditionalExplanation,\n      conditionalParentRuleId,\n    }),\n    description: generateRuleDescription(\n      'pattern',\n      nestedFieldName,\n      nameMappings,\n      'en',\n      {\n        ruleDependentValue: regex || enumValues,\n        conditionalExplanation,\n      }\n    ),\n    descriptionTranslationProposal: generateRuleDescription(\n      'pattern',\n      nestedFieldName,\n      nameMappings,\n      'de',\n      {\n        ruleDependentValue: regex || enumValues,\n        conditionalExplanation: translatedConditionalExplanation,\n      }\n    ),\n    type: 'pattern',\n    value: pattern,\n    target: nestedFieldName,\n  };\n}\n\nfunction getRequiredOneOfs(\n  definitions: Record<string, unknown>,\n  ref: string,\n  nestedFieldName: string\n): Record<string, Record<string, unknown>[]> {\n  const definition = getDefinition(definitions, ref);\n\n  if (definition['type'] !== 'object') {\n    return {};\n  }\n\n  const properties = definition['properties'];\n  const oneOf = definition['oneOf'];\n\n  if (!properties && !oneOf) {\n    return {};\n  }\n\n  if (isValidObject(properties)) {\n    const requiredProperties = getRequiredProperties(definition);\n    if (!requiredProperties) {\n      return {};\n    }\n    let requiredOneOfs: Record<string, Record<string, unknown>[]> = {};\n    for (const requiredProperty of requiredProperties) {\n      const property = properties[requiredProperty];\n      if (!isValidObject(property)) {\n        throw new Error(\n          `Invalid property format at ${nestedFieldName}-${requiredProperty}.`\n        );\n      }\n      const propertyRef = getRef(property);\n      if (!propertyRef) {\n        continue;\n      }\n      requiredOneOfs = {\n        ...requiredOneOfs,\n        ...getRequiredOneOfs(\n          definitions,\n          propertyRef,\n          `${nestedFieldName}-${requiredProperty}`\n        ),\n      };\n    }\n    return requiredOneOfs;\n  } else if (\n    Array.isArray(oneOf) &&\n    oneOf.every((entry) => isValidObject(entry))\n  ) {\n    if (!(oneOf.length > 1)) {\n      return {};\n    }\n    return {\n      [nestedFieldName]: oneOf,\n    };\n  }\n\n  throw new Error(`Invalid properties or oneOf at ${nestedFieldName}.`);\n}\n\nfunction getNestedFieldNamesPerOneOfBranch(\n  definitions: Record<string, unknown>,\n  oneOf: Record<string, unknown>[],\n  nestedFieldName: string\n): Record<string, string[]> {\n  const nestedFieldNamesPerBranch: Record<string, string[]> = {};\n\n  for (const oneOfEntry of oneOf) {\n    const data = getDataFromOneOfEntry(oneOfEntry);\n    if (!data) {\n      continue;\n    }\n    const { propertyKey, propertyRef } = data;\n\n    const oneOfEntryNestedFieldName = `${nestedFieldName}-${propertyKey}`;\n\n    const nestedFieldNames = getNestedFieldNames(\n      definitions,\n      propertyRef,\n      oneOfEntryNestedFieldName\n    );\n    nestedFieldNamesPerBranch[propertyKey] = nestedFieldNames;\n  }\n\n  return nestedFieldNamesPerBranch;\n}\n\nfunction getNestedRequiredRulesPerOneOfBranch(\n  definitions: Record<string, unknown>,\n  oneOf: Record<string, unknown>[],\n  nestedFieldName: string,\n  conditionalExplanation: string | undefined,\n  translatedConditionalExplanation: string | undefined,\n  conditionalParentRuleId: string | undefined,\n  nameMappings: XsdElement\n): Record<string, RequiredRule[]> {\n  const nestedRequiredRulesPerBranch: Record<string, RequiredRule[]> = {};\n\n  for (const oneOfEntry of oneOf) {\n    const data = getDataFromOneOfEntry(oneOfEntry);\n    if (!data) {\n      continue;\n    }\n    const { propertyKey, propertyRef } = data;\n\n    const oneOfEntryNestedFieldName = `${nestedFieldName}-${propertyKey}`;\n\n    const nestedRequiredRules: RequiredRule[] = getRules(\n      definitions,\n      propertyRef,\n      oneOfEntryNestedFieldName,\n      nameMappings,\n      {\n        globalRequired: true,\n        includeOnlyRequiredRules: true,\n        conditionalExplanation,\n        translatedConditionalExplanation,\n        conditionalParentRuleId,\n      }\n    ).filter((rule) => rule.type === 'required'); // Filter is only necessary so the \"RequiredRule\" type is correctly inferred.\n\n    nestedRequiredRulesPerBranch[propertyKey] = nestedRequiredRules;\n  }\n\n  return nestedRequiredRulesPerBranch;\n}\n\nfunction getNestedRequiredOneOfsPerOneOfBranch(\n  definitions: Record<string, unknown>,\n  oneOf: Record<string, unknown>[],\n  nestedFieldName: string\n): Record<string, Record<string, Record<string, unknown>[]>> {\n  const nestedRequiredOneOfsPerBranch: Record<\n    string,\n    Record<string, Record<string, unknown>[]>\n  > = {};\n\n  for (const oneOfEntry of oneOf) {\n    const data = getDataFromOneOfEntry(oneOfEntry);\n    if (!data) {\n      continue;\n    }\n    const { propertyKey, propertyRef } = data;\n\n    const oneOfEntryNestedFieldName = `${nestedFieldName}-${propertyKey}`;\n\n    const nestedRequiredOneOfs: Record<string, Record<string, unknown>[]> =\n      getRequiredOneOfs(definitions, propertyRef, oneOfEntryNestedFieldName);\n    nestedRequiredOneOfsPerBranch[propertyKey] = nestedRequiredOneOfs;\n  }\n\n  return nestedRequiredOneOfsPerBranch;\n}\n\n// Construct conditional 'required' rules for the oneOf entries, requiring all required fields on one branch of the tree if all fields in the other branches are not set.\n// This needs to make sure that oneOfs inside the branches are accounted for (they will not be included in the hypotheticalNestedRequiredRules)\nfunction getOneOfConditionalRequiredRules(\n  oneOf: Record<string, unknown>[],\n  definitions: Record<string, unknown>,\n  nestedFieldName: string,\n  nameMappings: XsdElement\n): ConditionalRule[] {\n  const nestedFieldNamesPerBranch: Record<string, string[]> =\n    getNestedFieldNamesPerOneOfBranch(definitions, oneOf, nestedFieldName);\n  const hypotheticalNestedRequiredRulesPerBranch: Record<\n    string,\n    RequiredRule[]\n  > = getNestedRequiredRulesPerOneOfBranch(\n    definitions,\n    oneOf,\n    nestedFieldName,\n    undefined, // Conditional explanations are added below.\n    undefined, // Translated conditional explanations are added below.\n    undefined, // Conditional parent nested field name is added below.\n    nameMappings\n  );\n  // It is not enough to retrieve the hypotheticalNestedRequiredRules. This works if there are no nested 'oneOf's. For nested 'oneOf's, we cannot retrieve a plain 'required' rule. It's \"either this or that\". So we need to construct conditional rules in this case.\n  // Contains for each branch (identified by the propertyKey of the branch's oneOf entry) and object that contains required oneOf entries on that branch. Each oneOf entry (Record<string, unknown>[]) is identified by its nestedFieldName.\n  const hypotheticalNestedRequiredOneOfsPerBranch: Record<\n    string,\n    Record<string, Record<string, unknown>[]>\n  > = getNestedRequiredOneOfsPerOneOfBranch(\n    definitions,\n    oneOf,\n    nestedFieldName\n  );\n  if (\n    Object.values(hypotheticalNestedRequiredOneOfsPerBranch).some(\n      (nestedRequiredOneOfs) => Object.entries(nestedRequiredOneOfs).length > 0\n    )\n  ) {\n    throw new Error(\n      `There are nested required oneOfs starting from ${nestedFieldName}. There is some logic to be implemented here that adds the necessary rules to handle this edge case.`\n    );\n  }\n\n  const conditionalRequiredRules: ConditionalRule[] = [];\n\n  for (const [propertyKey, nestedRequiredRules] of Object.entries(\n    hypotheticalNestedRequiredRulesPerBranch\n  )) {\n    // The condition for applying the required rules is that all fields in the other branches are not set.\n    const fieldNamesOfOtherBranches: string[] = Object.entries(\n      nestedFieldNamesPerBranch\n    )\n      .filter(([otherPropertyKey]) => otherPropertyKey !== propertyKey)\n      .flatMap(([, nestedFieldNames]) => nestedFieldNames);\n    const notPresentConditions: Condition[] = fieldNamesOfOtherBranches.map(\n      (fieldName) => ({\n        field: fieldName,\n        type: 'present',\n        value: false,\n      })\n    );\n\n    const conditionalRequiredRulesForBranch: ConditionalRule[] =\n      nestedRequiredRules.map((requiredRule) => {\n        const conditionalParentRuleId = generateRuleId(\n          'condition',\n          GENERATED_RULE_PREFIX,\n          requiredRule.target,\n          {\n            conditions: notPresentConditions,\n            conditionalRuleType: 'required',\n          }\n        );\n        const requiredRuleId = generateRuleId(\n          'required',\n          GENERATED_RULE_PREFIX,\n          requiredRule.target,\n          {\n            isConditional: true,\n            conditionalParentRuleId: conditionalParentRuleId,\n          }\n        );\n        const conditionalRequiredRules = [\n          {\n            ...requiredRule,\n            id: requiredRuleId,\n            description: generateRuleDescription(\n              'required',\n              requiredRule.target,\n              nameMappings,\n              'en',\n              {\n                ruleDependentValue: true,\n                conditionalExplanation: generateConditionalExplanation(\n                  notPresentConditions,\n                  'and',\n                  'en',\n                  nameMappings\n                ),\n              }\n            ),\n            descriptionTranslationProposal: generateRuleDescription(\n              'required',\n              requiredRule.target,\n              nameMappings,\n              'de',\n              {\n                ruleDependentValue: true,\n                conditionalExplanation: generateConditionalExplanation(\n                  notPresentConditions,\n                  'and',\n                  'de',\n                  nameMappings\n                ),\n              }\n            ),\n          },\n        ];\n        return {\n          id: conditionalParentRuleId,\n          description: generateRuleDescription(\n            'condition',\n            requiredRule.target,\n            nameMappings,\n            'en',\n            {\n              conditions: notPresentConditions,\n              conditionsConnector: 'and',\n              conditionalRules: conditionalRequiredRules,\n              conditionalRulesConnector: 'and',\n            }\n          ),\n          descriptionTranslationProposal: generateRuleDescription(\n            'condition',\n            requiredRule.target,\n            nameMappings,\n            'de',\n            {\n              conditions: notPresentConditions,\n              conditionsConnector: 'and',\n              conditionalRules: conditionalRequiredRules,\n              conditionalRulesConnector: 'and',\n            }\n          ),\n          type: 'condition',\n          target: requiredRule.target,\n          conditions: notPresentConditions,\n          conditionsConnector: 'and',\n          rules: conditionalRequiredRules,\n        };\n      });\n\n    conditionalRequiredRules.push(...conditionalRequiredRulesForBranch);\n  }\n  return conditionalRequiredRules;\n}\n\nfunction getDataFromOneOfEntry(oneOfEntry: Record<string, unknown>):\n  | {\n      propertyKey: string;\n      propertyRef: string;\n    }\n  | undefined {\n  const properties = oneOfEntry['properties'];\n  if (!isValidObject(properties) || Object.keys(properties).length !== 1) {\n    throw new Error(`Invalid properties format in oneOf entry: ${oneOfEntry}`);\n  }\n\n  const [propertyKey, propertyValue] = Object.entries(properties)[0];\n\n  if (!isValidObject(propertyValue)) {\n    throw new Error(`Invalid property format for \"${propertyKey}\".`);\n  }\n\n  const propertyRef = getRef(propertyValue);\n  if (!propertyRef) {\n    return undefined;\n  }\n\n  return {\n    propertyKey,\n    propertyRef,\n  };\n}\n\nfunction getPrimitiveRules(\n  type: unknown,\n  definition: Record<string, unknown>,\n  nestedFieldName: string,\n  nameMappings: XsdElement,\n  config: {\n    required: boolean;\n    includeOnlyRequiredRules: boolean;\n    conditionalExplanation: string | undefined;\n    translatedConditionalExplanation: string | undefined;\n    conditionalParentRuleId: string | undefined;\n  }\n): Rule[] {\n  const rules: Rule[] = [];\n\n  if (!(type === 'string' || type === 'boolean')) {\n    console.error(\n      `Invalid JSON schema: '${nestedFieldName}' is of type ${type}. This is not supported as of now.`\n    );\n    return rules;\n  }\n\n  if (type === 'string') {\n    const minLength = definition['minLength'];\n    const maxLength = definition['maxLength'];\n    const pattern = definition['pattern'];\n    const enumValues = definition['enum'];\n\n    let addedRequiredRule = false;\n\n    if (minLength) {\n      if (minLength !== 1) {\n        console.error(\n          `Invalid JSON schema: '${nestedFieldName}' has a minLength of ${minLength}, but only minLength of 1 is supported.`\n        );\n      } else if (config.required && !addedRequiredRule) {\n        rules.push(\n          getRequiredRule(\n            nestedFieldName,\n            config.conditionalExplanation,\n            config.translatedConditionalExplanation,\n            config.conditionalParentRuleId,\n            nameMappings\n          )\n        );\n        addedRequiredRule = true;\n      }\n    }\n\n    if (maxLength) {\n      if (typeof maxLength !== 'number') {\n        console.error(\n          `Invalid JSON schema: '${nestedFieldName}' has a maxLength of ${definition['maxLength']}, but only numeric maxLength is supported.`\n        );\n      } else if (!config.includeOnlyRequiredRules) {\n        rules.push({\n          id: generateRuleId(\n            'maxLength',\n            GENERATED_RULE_PREFIX,\n            nestedFieldName,\n            {\n              isConditional: !!config.conditionalExplanation,\n              conditionalParentRuleId: config.conditionalParentRuleId,\n            }\n          ),\n          description: generateRuleDescription(\n            'maxLength',\n            nestedFieldName,\n            nameMappings,\n            'en',\n            {\n              ruleDependentValue: definition['maxLength'],\n              conditionalExplanation: config.conditionalExplanation,\n            }\n          ),\n          descriptionTranslationProposal: generateRuleDescription(\n            'maxLength',\n            nestedFieldName,\n            nameMappings,\n            'de',\n            {\n              ruleDependentValue: definition['maxLength'],\n              conditionalExplanation: config.conditionalExplanation,\n            }\n          ),\n          type: 'maxLength',\n          value: maxLength,\n          target: nestedFieldName,\n        });\n      }\n    }\n\n    if (pattern) {\n      if (typeof pattern !== 'string') {\n        console.error(\n          `Invalid JSON schema: '${nestedFieldName}' has a pattern of ${pattern}, but only string patterns are supported.`\n        );\n      } else {\n        if (!config.includeOnlyRequiredRules) {\n          rules.push(\n            getPatternRule(\n              nestedFieldName,\n              pattern,\n              undefined,\n              config.conditionalExplanation,\n              config.translatedConditionalExplanation,\n              config.conditionalParentRuleId,\n              nameMappings\n            )\n          );\n        }\n        // This is an assumption we make: If a pattern is defined and the element is required, the field is required, even though the pattern may allow for the empty string.\n        if (config.required && !addedRequiredRule) {\n          rules.push(\n            getRequiredRule(\n              nestedFieldName,\n              config.conditionalExplanation,\n              config.translatedConditionalExplanation,\n              config.conditionalParentRuleId,\n              nameMappings\n            )\n          );\n          addedRequiredRule = true;\n        }\n      }\n    }\n\n    if (enumValues) {\n      if (\n        !Array.isArray(enumValues) ||\n        !enumValues.every((v) => typeof v === 'string')\n      ) {\n        console.error(\n          `Invalid JSON schema: '${nestedFieldName}' has an enum of ${enumValues}, but only string array enums are supported.`\n        );\n      } else {\n        if (!config.includeOnlyRequiredRules) {\n          rules.push(\n            getPatternRule(\n              nestedFieldName,\n              undefined,\n              enumValues,\n              config.conditionalExplanation,\n              config.translatedConditionalExplanation,\n              config.conditionalParentRuleId,\n              nameMappings\n            )\n          );\n        }\n        if (config.required && !addedRequiredRule) {\n          rules.push(\n            getRequiredRule(\n              nestedFieldName,\n              config.conditionalExplanation,\n              config.translatedConditionalExplanation,\n              config.conditionalParentRuleId,\n              nameMappings\n            )\n          );\n          addedRequiredRule = true;\n        }\n      }\n    }\n  } else if (definition['type'] === 'boolean') {\n    const enumValues = ['true', 'false'];\n    if (!config.includeOnlyRequiredRules) {\n      rules.push(\n        getPatternRule(\n          nestedFieldName,\n          undefined,\n          enumValues,\n          config.conditionalExplanation,\n          config.translatedConditionalExplanation,\n          config.conditionalParentRuleId,\n          nameMappings\n        )\n      );\n    }\n    if (config.required) {\n      rules.push(\n        getRequiredRule(\n          nestedFieldName,\n          config.conditionalExplanation,\n          config.translatedConditionalExplanation,\n          config.conditionalParentRuleId,\n          nameMappings\n        )\n      );\n    }\n  }\n\n  return rules;\n}\n\nfunction getArrayRules(\n  definitions: Record<string, unknown>,\n  propertyValue: Record<string, unknown>,\n  propertyRef: string,\n  fieldName: string,\n  nameMappings: XsdElement,\n  config: {\n    required: boolean;\n    includeOnlyRequiredRules: boolean;\n    conditionalExplanation: string | undefined;\n    translatedConditionalExplanation: string | undefined;\n    conditionalParentRuleId: string | undefined;\n  }\n): Rule[] {\n  // In addition to the MaxItemsRule that is required if 'maxItems' is defined, we generate nested rules. The logic for what exactly these mean for the array case, sits in the FormRulesDirective.\n  const arrayRules: Rule[] = [];\n\n  // MaxItemsRule\n  const maxItems = propertyValue['maxItems'];\n  if (maxItems) {\n    if (typeof maxItems !== 'number') {\n      throw new Error(\n        `Invalid JSON schema: '${fieldName}' has a maxItems of ${maxItems}, but only numeric maxItems is supported.`\n      );\n    }\n\n    if (!config.includeOnlyRequiredRules) {\n      arrayRules.push({\n        id: generateRuleId('maxItems', GENERATED_RULE_PREFIX, fieldName),\n        description: generateRuleDescription(\n          'maxItems',\n          fieldName,\n          nameMappings,\n          'en',\n          {\n            ruleDependentValue: maxItems,\n            conditionalExplanation: config.conditionalExplanation,\n          }\n        ),\n        descriptionTranslationProposal: generateRuleDescription(\n          'maxItems',\n          fieldName,\n          nameMappings,\n          'de',\n          {\n            ruleDependentValue: maxItems,\n            conditionalExplanation: config.conditionalExplanation,\n          }\n        ),\n        type: 'maxItems',\n        value: maxItems,\n        target: fieldName,\n      });\n    }\n  }\n\n  // Nested rules excluding RequiredRules for individual items' fields\n  arrayRules.push(\n    ...getRules(definitions, propertyRef, fieldName, nameMappings, {\n      globalRequired: config.required,\n      includeOnlyRequiredRules: config.includeOnlyRequiredRules,\n      conditionalExplanation: config.conditionalExplanation,\n      translatedConditionalExplanation: config.translatedConditionalExplanation,\n      conditionalParentRuleId: config.conditionalParentRuleId,\n    })\n  );\n\n  return arrayRules;\n}\n\nfunction getObjectPropertyRules(\n  definitions: Record<string, unknown>,\n  propertyKey: string,\n  propertyValue: Record<string, unknown>,\n  nestedFieldName: string,\n  nameMappings: XsdElement,\n  config: {\n    required: boolean;\n    includeOnlyRequiredRules: boolean;\n    conditionalExplanation: string | undefined;\n    translatedConditionalExplanation: string | undefined;\n    conditionalParentRuleId: string | undefined;\n  }\n): Rule[] {\n  const objectPropertyRules: Rule[] = [];\n\n  const propertyRef = getRef(propertyValue);\n\n  if (!propertyRef) {\n    // There might be a primitive rule defined here that is not extracted into its own definition.\n    objectPropertyRules.push(\n      ...getPrimitiveRules(\n        propertyValue['type'],\n        propertyValue,\n        nestedFieldName,\n        nameMappings,\n        {\n          required: config.required,\n          includeOnlyRequiredRules: config.includeOnlyRequiredRules,\n          conditionalExplanation: config.conditionalExplanation,\n          translatedConditionalExplanation:\n            config.translatedConditionalExplanation,\n          conditionalParentRuleId: config.conditionalParentRuleId,\n        }\n      )\n    );\n    return objectPropertyRules;\n  }\n\n  if ('type' in propertyValue) {\n    if (propertyValue['type'] !== 'array') {\n      throw new Error(\n        `Invalid JSON schema: '${propertyKey}' is of type ${propertyValue['type']} and there is an additional ref to ${propertyRef}. This is not supported as of now.`\n      );\n    }\n    objectPropertyRules.push(\n      ...getArrayRules(\n        definitions,\n        propertyValue,\n        propertyRef,\n        nestedFieldName,\n        nameMappings,\n        {\n          required: config.required,\n          includeOnlyRequiredRules: config.includeOnlyRequiredRules,\n          conditionalExplanation: config.conditionalExplanation,\n          translatedConditionalExplanation:\n            config.translatedConditionalExplanation,\n          conditionalParentRuleId: config.conditionalParentRuleId,\n        }\n      )\n    );\n  } else {\n    // Standard case: no 'type' defined, but a ref to another definition.\n    objectPropertyRules.push(\n      ...getRules(definitions, propertyRef, nestedFieldName, nameMappings, {\n        globalRequired: config.required,\n        includeOnlyRequiredRules: config.includeOnlyRequiredRules,\n        conditionalExplanation: config.conditionalExplanation,\n        translatedConditionalExplanation:\n          config.translatedConditionalExplanation,\n        conditionalParentRuleId: config.conditionalParentRuleId,\n      })\n    );\n  }\n\n  return objectPropertyRules;\n}\n\nfunction getConditionalRulesForRequiredOneOf(\n  definitions: Record<string, unknown>,\n  nestedFieldName: string,\n  requiredOneOf: Record<string, unknown>[],\n  orPresentConditions: Condition[],\n  nameMappings: XsdElement\n): ConditionalRule[] {\n  const conditionalExplanation = generateConditionalExplanation(\n    orPresentConditions,\n    'or',\n    'en',\n    nameMappings\n  );\n  const translatedConditionalExplanation = generateConditionalExplanation(\n    orPresentConditions,\n    'or',\n    'de',\n    nameMappings\n  );\n\n  // Already generate the parent rule ID here, so we can add it to the rule IDs of the nested \"required\" rules.\n  const conditionalParentRuleId = generateRuleId(\n    'condition',\n    GENERATED_RULE_PREFIX,\n    nestedFieldName,\n    { conditions: orPresentConditions, conditionalRuleType: 'required' }\n  );\n\n  // Retrieve required rules for both sides.\n  const nestedRequiredRulesPerBranch: Record<string, RequiredRule[]> =\n    getNestedRequiredRulesPerOneOfBranch(\n      definitions,\n      requiredOneOf,\n      nestedFieldName,\n      conditionalExplanation,\n      translatedConditionalExplanation,\n      conditionalParentRuleId,\n      nameMappings\n    );\n\n  // Retrieve required oneOfs for both sides.\n  const nestedRequiredOneOfsPerBranch: Record<\n    string,\n    Record<string, Record<string, unknown>[]>\n  > = getNestedRequiredOneOfsPerOneOfBranch(\n    definitions,\n    requiredOneOf,\n    nestedFieldName\n  );\n\n  // Just a sanity check\n  const numberOfOneOfOptions = Object.keys(requiredOneOf).length;\n  if (numberOfOneOfOptions < 2) {\n    throw new Error(`Unexpected oneOf with ${numberOfOneOfOptions} entries.`);\n  }\n  if (\n    Object.keys(nestedRequiredRulesPerBranch).length !== numberOfOneOfOptions ||\n    Object.keys(nestedRequiredOneOfsPerBranch).length !== numberOfOneOfOptions\n  ) {\n    throw new Error(`Unexpected number of nested entries.`);\n  }\n\n  const hasNestedRequiredRules = Object.values(\n    nestedRequiredRulesPerBranch\n  ).every(\n    (listOfRequiredRulesForBranch) => listOfRequiredRulesForBranch.length > 0\n  );\n  const hasNestedRequiredOneOfs = Object.values(\n    nestedRequiredOneOfsPerBranch\n  ).every(\n    (listOfRequiredOneOfsForBranch) =>\n      Object.keys(listOfRequiredOneOfsForBranch).length > 0\n  );\n\n  // If there are no required rules and no required oneOfs, no rule needs to be constructed.\n  if (!hasNestedRequiredRules && !hasNestedRequiredOneOfs) {\n    return [];\n  }\n\n  // If there is exactly one required rule per side and there are no required oneOfs, we can construct a conditional rule using the original orPresentConditions and stick the required rules in there with an \"or\" rulesConnector.\n  if (\n    Object.values(nestedRequiredRulesPerBranch).every(\n      (listOfRequiredRulesForBranch) =>\n        listOfRequiredRulesForBranch.length === 1\n    ) &&\n    !hasNestedRequiredOneOfs\n  ) {\n    const conditionalRequiredRules = Object.values(\n      nestedRequiredRulesPerBranch\n    ).map((listOfRequiredRulesForBranch) => listOfRequiredRulesForBranch[0]);\n    return [\n      {\n        id: conditionalParentRuleId,\n        description: generateRuleDescription(\n          'condition',\n          nestedFieldName,\n          nameMappings,\n          'en',\n          {\n            conditions: orPresentConditions,\n            conditionsConnector: 'or',\n            conditionalRules: conditionalRequiredRules,\n            conditionalRulesConnector: 'or',\n          }\n        ),\n        descriptionTranslationProposal: generateRuleDescription(\n          'condition',\n          nestedFieldName,\n          nameMappings,\n          'de',\n          {\n            conditions: orPresentConditions,\n            conditionsConnector: 'or',\n            conditionalRules: conditionalRequiredRules,\n            conditionalRulesConnector: 'or',\n          }\n        ),\n        type: 'condition',\n        conditions: orPresentConditions,\n        conditionsConnector: 'or',\n        rules: conditionalRequiredRules,\n        rulesConnector: 'or',\n      },\n    ];\n  }\n\n  // There are a lot more options here that are just not present in the JSON schema we are currently handling (pacs.008).\n  // However, there might be more logic to implement here in the future.\n  // See concepts/required-one-of-conditional-rules.drawio for some considerations. Only the green ones are implemented so far.\n  throw new Error(\n    `Unsupported combination of nested required rules and nested required oneOfs for ${nestedFieldName}.`\n  );\n}\n\nfunction getConditionalRequiredRulesForNotGloballyRequiredProperty(\n  definitions: Record<string, unknown>,\n  objectProperties: Record<string, unknown>,\n  requiredPropertyKey: string,\n  nestedFieldName: string,\n  nameMappings: XsdElement\n): ConditionalRule[] {\n  const requiredProperty = objectProperties[requiredPropertyKey];\n  if (!isValidObject(requiredProperty)) {\n    throw new Error(\n      `Invalid JSON schema: '${requiredPropertyKey}' is not a valid object`\n    );\n  }\n  const otherProperties = Object.entries(objectProperties).filter(\n    ([key]) => key !== requiredPropertyKey\n  );\n  const otherNestedFieldNames = otherProperties.reduce<string[]>(\n    (acc, [propertyKey, propertyValue]) => {\n      if (!isValidObject(propertyValue)) {\n        throw new Error(\n          `Invalid JSON schema: '${propertyKey}' is not a valid object`\n        );\n      }\n      const propertyNestedFieldName = `${nestedFieldName}-${propertyKey}`;\n      const propertyRef = getRef(propertyValue);\n      if (!propertyRef) {\n        acc.push(propertyNestedFieldName);\n        return acc;\n      }\n      acc.push(\n        ...getNestedFieldNames(\n          definitions,\n          propertyRef,\n          propertyNestedFieldName\n        )\n      );\n      return acc;\n    },\n    []\n  );\n  const presentConditions: Condition[] = otherNestedFieldNames.map(\n    (fieldName) => ({\n      field: fieldName,\n      type: 'present',\n      value: true,\n    })\n  );\n  if (presentConditions.length === 0) {\n    // There are no conditions. It doesn't make sense to construct a rule in that case.\n    return [];\n  }\n\n  const conditionalExplanation = generateConditionalExplanation(\n    presentConditions,\n    'or',\n    'en',\n    nameMappings\n  );\n  const translatedConditionalExplanation = generateConditionalExplanation(\n    presentConditions,\n    'or',\n    'de',\n    nameMappings\n  );\n\n  const requiredPropertyRef = getRef(requiredProperty);\n  const requiredPropertyNestedFieldName = `${nestedFieldName}-${requiredPropertyKey}`;\n\n  const conditionalParentRuleId = generateRuleId(\n    'condition',\n    GENERATED_RULE_PREFIX,\n    requiredPropertyNestedFieldName,\n    { conditions: presentConditions, conditionalRuleType: 'required' }\n  );\n  const requiredRules: RequiredRule[] = !requiredPropertyRef\n    ? [\n        getRequiredRule(\n          requiredPropertyNestedFieldName,\n          conditionalExplanation,\n          translatedConditionalExplanation,\n          conditionalParentRuleId,\n          nameMappings\n        ),\n      ]\n    : getRules(\n        definitions,\n        requiredPropertyRef,\n        requiredPropertyNestedFieldName,\n        nameMappings,\n        {\n          globalRequired: true,\n          includeOnlyRequiredRules: true,\n          conditionalExplanation,\n          translatedConditionalExplanation,\n          conditionalParentRuleId,\n        }\n      ).filter((rule) => rule.type === 'required');\n\n  // Check if there are nested oneOf rules. E.g. \"FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct\" is treated in this function.\n  // \"FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct\" is not globally required and \"SttlmAcct-Id\" is the only required property.\n  // It defines a choice between \"SttlmAcct-Id-IBAN\" and \"SttlmAcct-Id-Othr\" but we did not get any of these from the 'getRules' above ('globalRequired' is always false for 'oneOf').\n  // And we also wouldn't know if the required rules we got above were connected by \"or\" or \"and\". The way 'getRules' is implemented, it is always an \"and\", so we treat the 'oneOf' case with \"or\" separately.\n  const nestedRequiredOneOfs: Record<string, Record<string, unknown>[]> =\n    !requiredPropertyRef\n      ? {}\n      : getRequiredOneOfs(\n          definitions,\n          requiredPropertyRef,\n          requiredPropertyNestedFieldName\n        );\n\n  if (Object.entries(nestedRequiredOneOfs).length > 1) {\n    throw new Error(\n      `There is more than one nested required oneOf at ${requiredPropertyNestedFieldName}. This is not supported as of now.`\n    );\n  }\n\n  if (\n    Object.entries(nestedRequiredOneOfs).length === 0 &&\n    requiredRules.length === 0\n  ) {\n    // Nothing is really required for the required property, so we just don't add any rules in that case.\n    return [];\n  }\n\n  if (Object.entries(nestedRequiredOneOfs).length === 0) {\n    return [\n      {\n        id: conditionalParentRuleId,\n        description: generateRuleDescription(\n          'condition',\n          requiredPropertyNestedFieldName,\n          nameMappings,\n          'en',\n          {\n            conditions: presentConditions,\n            conditionsConnector: 'or',\n            conditionalRules: requiredRules,\n            conditionalRulesConnector: 'and',\n          }\n        ),\n        descriptionTranslationProposal: generateRuleDescription(\n          'condition',\n          requiredPropertyNestedFieldName,\n          nameMappings,\n          'de',\n          {\n            conditions: presentConditions,\n            conditionsConnector: 'or',\n            conditionalRules: requiredRules,\n            conditionalRulesConnector: 'and',\n          }\n        ),\n        type: 'condition',\n        conditions: presentConditions,\n        conditionsConnector: 'or',\n        rules: requiredRules,\n        rulesConnector: 'and',\n      },\n    ];\n  }\n\n  if (requiredRules.length === 0) {\n    const [requiredOneOfNestedFieldName, requiredOneOf] =\n      Object.entries(nestedRequiredOneOfs)[0];\n    return getConditionalRulesForRequiredOneOf(\n      definitions,\n      requiredOneOfNestedFieldName,\n      requiredOneOf,\n      presentConditions,\n      nameMappings\n    );\n  }\n\n  throw new Error(\n    `Unexpected combination of required rules and required oneOfs: ${requiredRules}, ${nestedRequiredOneOfs}`\n  );\n}\n\nfunction getObjectPropertiesRules(\n  definitions: Record<string, unknown>,\n  definition: Record<string, unknown>,\n  nestedFieldName: string,\n  nameMappings: XsdElement,\n  config: {\n    required: boolean;\n    includeOnlyRequiredRules: boolean;\n    conditionalExplanation: string | undefined;\n    translatedConditionalExplanation: string | undefined;\n    conditionalParentRuleId: string | undefined;\n  }\n): Rule[] {\n  const objectProperties = getObjectProperties(definition);\n  const requiredProperties = getRequiredProperties(definition);\n\n  const objectPropertiesRules: Rule[] = [];\n\n  for (const [propertyKey, propertyValue] of Object.entries(objectProperties)) {\n    if (!isValidObject(propertyValue)) {\n      throw new Error(\n        `Invalid JSON schema: '${propertyKey}' is not a valid object`\n      );\n    }\n\n    objectPropertiesRules.push(\n      ...getObjectPropertyRules(\n        definitions,\n        propertyKey,\n        propertyValue,\n        `${nestedFieldName}-${propertyKey}`,\n        nameMappings,\n        {\n          required:\n            config.required &&\n            (requiredProperties?.includes(propertyKey) ?? false),\n          includeOnlyRequiredRules: config.includeOnlyRequiredRules,\n          conditionalExplanation: config.conditionalExplanation,\n          translatedConditionalExplanation:\n            config.translatedConditionalExplanation,\n          conditionalParentRuleId: config.conditionalParentRuleId,\n        }\n      )\n    );\n  }\n\n  // Add conditional required rules for all required properties if the current level is not globally required (for each required property, there must be a rule that requires it if any of the other fields are set). In the 'globally required' case, we create the basic required rules directly above.\n  if (\n    !config.required &&\n    requiredProperties &&\n    !config.includeOnlyRequiredRules &&\n    Object.entries(objectProperties).length > 1 // This only makes sense if there is more than one property.\n  ) {\n    for (const requiredPropertyKey of requiredProperties) {\n      objectPropertiesRules.push(\n        ...getConditionalRequiredRulesForNotGloballyRequiredProperty(\n          definitions,\n          objectProperties,\n          requiredPropertyKey,\n          nestedFieldName,\n          nameMappings\n        )\n      );\n    }\n  }\n\n  return objectPropertiesRules;\n}\n\nfunction getRules(\n  definitions: Record<string, unknown>,\n  ref: string,\n  nestedFieldName: string,\n  nameMappings: XsdElement,\n  config: {\n    globalRequired: boolean;\n    includeOnlyRequiredRules: boolean;\n    conditionalExplanation: string | undefined;\n    translatedConditionalExplanation: string | undefined;\n    conditionalParentRuleId: string | undefined;\n  }\n): Rule[] {\n  const definition = getDefinition(definitions, ref);\n\n  const rules: Rule[] = [];\n\n  if (definition['type'] === 'string' || definition['type'] === 'boolean') {\n    rules.push(\n      ...getPrimitiveRules(\n        definition['type'],\n        definition,\n        nestedFieldName,\n        nameMappings,\n        {\n          required: config.globalRequired,\n          includeOnlyRequiredRules: config.includeOnlyRequiredRules,\n          conditionalExplanation: config.conditionalExplanation,\n          translatedConditionalExplanation:\n            config.translatedConditionalExplanation,\n          conditionalParentRuleId: config.conditionalParentRuleId,\n        }\n      )\n    );\n  } else if (definition['type'] === 'array') {\n    throw new Error(\n      `Array type on definition level is not supported. Error at field ${nestedFieldName}.`\n    );\n  } else if (definition['type'] === 'object') {\n    const properties = definition['properties'];\n    const oneOf = definition['oneOf'];\n\n    if (!properties && !oneOf) {\n      return rules;\n    }\n\n    const nestedRules: Rule[] = [];\n\n    if (isValidObject(properties)) {\n      nestedRules.push(\n        ...getObjectPropertiesRules(\n          definitions,\n          definition,\n          nestedFieldName,\n          nameMappings,\n          {\n            required: config.globalRequired,\n            includeOnlyRequiredRules: config.includeOnlyRequiredRules,\n            conditionalExplanation: config.conditionalExplanation,\n            translatedConditionalExplanation:\n              config.translatedConditionalExplanation,\n            conditionalParentRuleId: config.conditionalParentRuleId,\n          }\n        )\n      );\n    } else if (Array.isArray(oneOf)) {\n      if (!oneOf.every((entry) => isValidObject(entry))) {\n        throw new Error(`Invalid oneOf entry in oneOf ${oneOf}`);\n      }\n\n      for (const oneOfEntry of oneOf) {\n        const data = getDataFromOneOfEntry(oneOfEntry);\n\n        if (!data) {\n          continue;\n        }\n\n        const { propertyKey, propertyRef } = data;\n\n        nestedRules.push(\n          ...getRules(\n            definitions,\n            propertyRef,\n            `${nestedFieldName}-${propertyKey}`,\n            nameMappings,\n            {\n              globalRequired: oneOf.length > 1 ? false : config.globalRequired, // 'oneOf' usually means that the sub properties are not globally required. However, if there is only one entry in the 'oneOf' array, the entry might be required.\n              includeOnlyRequiredRules: config.includeOnlyRequiredRules,\n              conditionalExplanation: config.conditionalExplanation,\n              translatedConditionalExplanation:\n                config.translatedConditionalExplanation,\n              conditionalParentRuleId: config.conditionalParentRuleId,\n            }\n          )\n        );\n      }\n\n      if (config.globalRequired && !config.includeOnlyRequiredRules) {\n        // We need to construct conditional 'required' rules for the oneOf entries, requiring all required fields on one branch of the tree if all fields in the other branches are not set.\n        nestedRules.push(\n          ...getOneOfConditionalRequiredRules(\n            oneOf,\n            definitions,\n            nestedFieldName,\n            nameMappings\n          )\n        );\n      }\n\n      if (!config.includeOnlyRequiredRules) {\n        // We could also construct conditional 'prohibited' rules for the oneOf entries, prohibiting all fields on one branch of the tree if at least one field in another branch is set.\n        // However, this produces a lot of rules and is not strictly necessary as the client code automatically wipes the other branch's values when switching.\n      }\n    }\n\n    rules.push(...nestedRules);\n  } else {\n    console.error(\n      `Invalid JSON schema: '${nestedFieldName}' is of type ${definition['type']}. This is not supported as of now.`\n    );\n  }\n\n  return rules;\n}\n\nexport function generateRules(\n  jsonSchema: Record<string, unknown>,\n  nameMappings: XsdElement\n): Rule[] {\n  const basePropertyAbbreviatedName = nameMappings.abbrName;\n  if (!('properties' in jsonSchema && 'definitions' in jsonSchema)) {\n    throw new Error(\n      \"Invalid JSON schema: 'properties' and 'definitions' not found\"\n    );\n  }\n\n  const properties = jsonSchema['properties'];\n  if (!isValidObject(properties)) {\n    throw new Error(\"Invalid JSON schema: 'properties' is not a valid object\");\n  }\n\n  const definitions = jsonSchema['definitions'];\n  if (!isValidObject(definitions)) {\n    throw new Error(\"Invalid JSON schema: 'definitions' is not a valid object\");\n  }\n\n  const baseProperty = properties[basePropertyAbbreviatedName];\n  if (!isValidObject(baseProperty)) {\n    throw new Error(\n      `Invalid JSON schema: '${basePropertyAbbreviatedName}' is not a valid object`\n    );\n  }\n\n  const basePropertyRef = getRef(baseProperty);\n  if (!basePropertyRef) {\n    throw new Error(\n      `Invalid JSON schema: '${basePropertyAbbreviatedName}' does not have a valid reference`\n    );\n  }\n\n  return getRules(\n    definitions,\n    basePropertyRef,\n    basePropertyAbbreviatedName,\n    nameMappings,\n    {\n      globalRequired: true,\n      includeOnlyRequiredRules: false,\n      conditionalExplanation: undefined,\n      translatedConditionalExplanation: undefined,\n      conditionalParentRuleId: undefined,\n    }\n  );\n}"}]}], "uncommittedChanges": [{"path": "projects/test-app/src/app/components/form-group-array/form-group-array.component.html", "status": "A", "content": "<app-base-field\n  [label]=\"label()\"\n  [fieldNames]=\"[fieldName()]\"\n  [isReadOnly]=\"isReadOnly()\"\n  [useFieldset]=\"true\"\n>\n  <div class=\"formArrayContainer\">\n    <div [formArrayName]=\"fieldName()\">\n      @for (control of controls(); let index = $index; track index;) {\n      <div [formGroupName]=\"index\" class=\"formArrayItem\">\n        <div class=\"formArrayItemHeader\">\n          <h4>Item {{ index + 1 }}</h4>\n          <p-button\n            icon=\"pi pi-times\"\n            [rounded]=\"true\"\n            [text]=\"true\"\n            severity=\"danger\"\n            (click)=\"removeItem(index)\"\n          />\n        </div>\n        <div>\n          <!-- Custom template content -->\n          <ng-container\n            *ngTemplateOutlet=\"\n            itemTemplate();\n            context: {\n              $implicit: control,\n              fieldPrefix: `${fieldName()}.${index}`\n            }\n          \"\n          />\n        </div>\n      </div>\n      }\n    </div>\n    <p-button icon=\"pi pi-plus\" (click)=\"addItem()\" label=\"Add Item\" />\n  </div>\n</app-base-field>\n"}, {"path": "projects/test-app/src/app/components/form-group-array/form-group-array.component.scss", "status": "A", "content": ".formArrayContainer {\n  margin-top: 0.5rem;\n}\n\n.formArrayItem {\n  border: 2px solid var(--p-stone-200);\n  border-radius: 4px;\n  margin-bottom: 0.5rem;\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n\n  &Header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n\n    h4 {\n      font-weight: 200;\n    }\n  }\n}\n"}, {"path": "projects/test-app/src/app/components/form-group-array/form-group-array.component.ts", "status": "A", "content": "import {\n  ChangeDetectionStrategy,\n  Component,\n  computed,\n  inject,\n  input,\n  TemplateRef,\n} from '@angular/core';\nimport { BaseFieldComponent } from '../base-field';\nimport {\n  ControlContainer,\n  FormArray,\n  FormGroup,\n  FormGroupDirective,\n  NonNullableFormBuilder,\n  ReactiveFormsModule,\n} from '@angular/forms';\nimport { ButtonModule } from 'primeng/button';\nimport { NgTemplateOutlet } from '@angular/common';\n\n@Component({\n  selector: 'app-form-group-array',\n  imports: [\n    ReactiveFormsModule,\n    BaseFieldComponent,\n    ButtonModule,\n    NgTemplateOutlet,\n  ],\n  templateUrl: './form-group-array.component.html',\n  styleUrl: './form-group-array.component.scss',\n  viewProviders: [\n    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree, required to use 'formControlName' within the component.\n  ],\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class FormGroupArrayComponent extends BaseFieldComponent {\n  controlContainer = inject(ControlContainer);\n  fb = inject(NonNullableFormBuilder);\n\n  // Factory function to create new form groups\n  groupFactory = input.required<() => FormGroup>();\n  // Template for rendering each group item\n  itemTemplate = input.required<TemplateRef<unknown>>();\n  // Labels (required for adding labels to leaf controls)\n  labels = input.required<Record<string, string>>();\n\n  get formGroup(): FormGroup {\n    return this.controlContainer.control as FormGroup;\n  }\n\n  fieldName = computed(() => {\n    const fieldNames = this.fieldNames();\n    if (fieldNames.length > 1) {\n      throw new Error(\n        'FormGroupArrayComponent can only handle a single field name.'\n      );\n    }\n    return fieldNames[0];\n  });\n\n  formArray = computed(() => {\n    const field = this.formGroup.get(this.fieldName());\n    if (field && field instanceof FormArray) {\n      return field;\n    }\n    throw new Error(\n      `Expected to find a FormArray for field name ${this.fieldName()}`\n    );\n  });\n\n  controls = computed(() => {\n    const array = this.formArray();\n    const arrayControls = array.controls;\n    if (!arrayControls || !Array.isArray(arrayControls)) {\n      throw new Error(\n        `Expected the formArray to contain a list of controls for field name ${this.fieldName()}`\n      );\n    }\n    return arrayControls;\n  });\n\n  addItem() {\n    const fieldArray = this.formArray();\n    if (fieldArray) {\n      const newGroup = this.groupFactory()();\n      fieldArray.push(newGroup);\n    }\n  }\n\n  removeItem(index: number) {\n    const fieldArray = this.formArray();\n    if (fieldArray) {\n      fieldArray.removeAt(index);\n    }\n  }\n}\n"}, {"path": "projects/test-app/src/app/components/form-group-array/index.ts", "status": "A", "content": "export * from './form-group-array.component';\n"}]}