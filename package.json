{"name": "lib", "version": "0.0.0", "description": "Angular library for ISO20022 message handling", "scripts": {"setup": "npm run internal:install-all", "start": "npm run test-app:start", "generate-rules": "npm run internal:generate-rules && npm run lib:build", "build": "npm run lib:build", "reset": "npm run internal:reset-all", "lib:install": "cd projects/iso20022-lib && npm install", "lib:build": "cd projects/iso20022-lib && npm run build", "test-app:install": "cd projects/test-app && npm install", "test-app:start": "cd projects/test-app && npm run start", "root:install": "npm install", "internal:install-all": "npm run lib:install && npm run lib:build && npm run test-app:install && npm run root:install", "internal:generate-rules": "ts-node -P rule-generation/tsconfig.rule-generation.json rule-generation/scripts/main.ts rule-generation/input/pacs.008/pacs.008.001.08_cbprplus/schema.json rule-generation/input/pacs.008/pacs.008.001.08_cbprplus/schema.xsd rule-generation/input/pacs.008/form-setup.json --output-folder projects/iso20022-lib/rules/generated --base-element fi_to_fi_customer_credit_transfer_v08", "internal:reset-all": "rm -rf node_modules package-lock.json dist projects/**/node_modules projects/**/package-lock.json", "internal:export-import-changes": "ts-node -P scripts/tsconfig.scripts.json scripts/export-import-changes.ts"}, "private": true, "devDependencies": {"@helaba/iso20022-lib": "file:./dist/helaba-iso20022-lib-0.0.1.tgz", "@types/minimist": "^1.2.5", "@types/node": "^22.15.29", "@xmldom/xmldom": "^0.9.8", "minimist": "^1.2.8", "ts-node": "^10.9.2", "tslib": "^2.8.1", "tsconfig-paths": "^4.2.0", "typescript": "~5.7.2"}}