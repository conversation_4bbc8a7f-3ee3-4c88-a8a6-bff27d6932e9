# ISO20022-Angular-Lib

## Projects

- projects/iso20022-lib
  - FormRule-Directive
  - Audohide-Directive
  - FieldErrorsComponent
  - FormErrorsService
  - clientRules
  - affectedFields
  - types for rules
- projects/test-app

  - For testing/developing the lib

## Available Scripts

This repository uses a manual monorepo setup with user-facing scripts defined in the root `package.json`.

### Main Commands

| Command                  | Description                                      |
| ------------------------ | ------------------------------------------------ |
| `npm run setup`          | Installs all dependencies and builds the library |
| `npm run start`          | Starts the test app for development              |
| `npm run generate-rules` | Regenerates ISO 20022 rules and rebuilds the lib |
| `npm run build`          | Rebuilds the lib                                 |
| `npm run reset`          | Deletes all `node_modules` and lock files        |

## Develop lib

- All components/directves etc. in the `iso20022-lib` project need their own ng-package.json, public-api.ts, and index.ts
- All lib components must be imported via `@helaba/foo` (even within the lib), see tsconfig.json.

## Use the `Rule` types and rules in a client project

```bash
npm install <path/to/lib>/dist/helaba-iso20022-lib-0.0.1.tgz
```

## Git Export/Import Scripts

Scripts for transferring Git changes between environments when only text content can be copied.

## Usage

### Exporting Changes

```bash
# Export only uncommitted changes
npm run internal:export-import-changes

# Export last 2 commits + uncommitted changes
npm run internal:export-import-changes -- 2
```

Creates `git-changes-export.json` in project root.

### Importing Changes

1. Copy content from `git-changes-export.json`
2. Paste into `git-changes-export.json` in target environment
3. Run: `npm run internal:export-import-changes`

### Features

- Preserves commit messages, timestamps, and order
- Handles file creation, modification, and deletion
- Validates timestamps to prevent importing old changes
- Creates directory structures automatically
- Blocks import if uncommitted changes exist
