[{"id": "FIToFICstmrCdtTrf-GrpHdr-MsgId__serverOnly", "description": "GrpHdr-MsgId is serverOnly.", "descriptionTranslationProposal": "GrpHdr-MsgId ist vom Server zu befüllen.", "type": "serverOnly", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-MsgId"}, {"id": "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-UETR__serverOnly", "description": "CdtTrfTxInf-PmtId-UETR is serverOnly.", "descriptionTranslationProposal": "CdtTrfTxInf-PmtId-UETR ist vom Server zu befüllen.", "type": "serverOnly", "value": true, "target": "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-UETR"}, {"id": "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-ClrSysRef__serverOnly", "description": "CdtTrfTxInf-PmtId-ClrSysRef is serverOnly.", "descriptionTranslationProposal": "CdtTrfTxInf-PmtId-ClrSysRef ist vom Server zu befüllen.", "type": "serverOnly", "value": true, "target": "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-ClrSysRef"}, {"id": "FIToFICstmrCdtTrf-GrpHdr-NbOfTxs__serverOnly", "description": "GrpHdr-NbOfTxs is serverOnly.", "descriptionTranslationProposal": "GrpHdr-NbOfTxs ist vom Server zu befüllen.", "type": "serverOnly", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-NbOfTxs"}, {"id": "FIToFICstmrCdtTrf-GrpHdr-NbOfTxs__value", "target": "FIToFICstmrCdtTrf-GrpHdr-NbOfTxs", "description": "GrpHdr-NbOfTxs must equal \"1\".", "descriptionTranslationProposal": "GrpHdr-NbOfTxs muss gleich \"1\" sein.", "type": "value", "value": "1", "isEqual": true}, {"id": "R12__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry__conditional-required__7e2b02f6", "description": "If FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm is present, then FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry must also be present. (R12)", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm gesetzt ist, dann ist CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-<PERSON><PERSON> erforderlich. (R12)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm"}], "rules": [{"id": "R12__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry__conditional-required__7e2b02f6+FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry__required-conditional", "description": "If FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm is present, then FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry must also be present. (R12)", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry ist erforderlich, wenn CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm gesetzt ist. (R12)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry"}], "rulesConnector": "and"}, {"id": "R12__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__28b43e7a", "description": "If CdtTrfTxInf-CdtrAgt-FinInstnId-Nm is set, then CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-(AdrLine or TwnNm) is required. (R12)", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-CdtrAgt-FinInstnId-Nm gesetzt ist, dann ist CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-(AdrLine oder TwnNm) erforderlich. (R12)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm"}], "rules": [{"id": "R12__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__28b43e7a+FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine__required-conditional", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-(AdrLine or TwnNm) is required if CdtTrfTxInf-CdtrAgt-FinInstnId-Nm is set. (R12)", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-(AdrLine oder TwnNm) ist erforderlich, wenn CdtTrfTxInf-CdtrAgt-FinInstnId-Nm gesetzt ist. (R12)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine"}, {"id": "R12__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__28b43e7a+FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm__required-conditional", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-(TwnNm or AdrLine) is required if CdtTrfTxInf-CdtrAgt-FinInstnId-Nm is set. (R12)", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-(TwnNm oder AdrLine) ist erforderlich, wenn CdtTrfTxInf-CdtrAgt-FinInstnId-Nm gesetzt ist. (R12)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm"}], "rulesConnector": "or"}, {"id": "R12__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm__conditional-required__581a5fa9", "description": "If CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr is present, then CdtTrfTxInf-CdtrAgt-FinInstnId-Nm is required. (R12)", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr vorhanden ist, dann ist CdtTrfTxInf-CdtrAgt-FinInstnId-Nm erforderlich. (R12)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine"}], "rules": [{"id": "R12__FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm__conditional-required__581a5fa9+FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm__required-conditional", "description": "CdtTrfTxInf-CdtrAgt-FinInstnId-Nm is required if CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr is present. (R12)", "descriptionTranslationProposal": "CdtTrfTxInf-CdtrAgt-FinInstnId-Nm ist erford<PERSON>lich, wenn CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr vorhanden ist. (R12)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm"}], "conditionsConnector": "or"}, {"id": "R13__FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd__conditional-value__84722f58", "description": "If CdtTrfTxInf-InstrForCdtrAgt-Cd has the value \"CHQB\", then CdtTrfTxInf-InstrForCdtrAgt-Cd must not equal \"HOLD\". (R13)", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-InstrForCdtrAgt-Cd den Wert \"CHQB\" hat, dann muss CdtTrfTxInf-InstrForCdtrAgt-Cd ungleich \"HOLD\" sein. (R13)", "type": "condition", "conditions": [{"type": "value", "value": "CHQB", "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd"}], "rules": [{"id": "R13__FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd__conditional-value__84722f58+FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd__value-conditional", "description": "CdtTrfTxInf-InstrForCdtrAgt-Cd must not equal \"HOLD\" if CdtTrfTxInf-InstrForCdtrAgt-Cd has the value \"CHQB\". (R13)", "descriptionTranslationProposal": "CdtTrfTxInf-InstrForCdtrAgt-Cd muss ungleich \"HOLD\" sein, wenn CdtTrfTxInf-InstrForCdtrAgt-Cd den Wert \"CHQB\" hat. (R13)", "type": "value", "value": "HOLD", "isEqual": false, "target": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd"}]}, {"id": "R14__FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd__conditional-value__84722f58", "description": "If CdtTrfTxInf-InstrForCdtrAgt-Cd has the value \"PHOB\", then CdtTrfTxInf-InstrForCdtrAgt-Cd must not equal \"TELB\". (R14)", "descriptionTranslationProposal": "Wenn CdtTrfTxInf-InstrForCdtrAgt-Cd den Wert \"PHOB\" hat, dann muss CdtTrfTxInf-InstrForCdtrAgt-Cd ungleich \"TELB\" sein. (R14)", "type": "condition", "conditions": [{"type": "value", "value": "PHOB", "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd"}], "rules": [{"id": "R14__FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd__conditional-value__84722f58+FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd__value-conditional", "description": "CdtTrfTxInf-InstrForCdtrAgt-Cd must not equal \"TELB\" if CdtTrfTxInf-InstrForCdtrAgt-Cd has the value \"PHOB\". (R14)", "descriptionTranslationProposal": "CdtTrfTxInf-InstrForCdtrAgt-Cd muss ungleich \"TELB\" sein, wenn CdtTrfTxInf-InstrForCdtrAgt-Cd den Wert \"PHOB\" hat. (R14)", "type": "value", "value": "TELB", "isEqual": false, "target": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd"}]}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408", "description": "If GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI is set, then GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(Nm and ClrSysMmbId and PstlAdr) is prohibited. (R16)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist, dann ist GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(Nm und ClrSysMmbId und PstlAdr) nicht erlaubt. (R16)", "type": "condition", "conditions": [{"field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI", "type": "present", "value": true}], "rules": [{"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(Nm and ClrSysMmbId and PstlAdr) is prohibited if GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(Nm und ClrSysMmbId und PstlAdr) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(ClrSysMmbId and Nm and PstlAdr) is prohibited if GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(ClrSysMmbId und Nm und PstlAdr) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(ClrSysMmbId and Nm and PstlAdr) is prohibited if GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(ClrSysMmbId und Nm und PstlAdr) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__2bd65408+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}], "rulesConnector": "and"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb", "description": "If GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI is set, then GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(Nm and ClrSysMmbId and PstlAdr) is prohibited. (R16)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist, dann ist GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(Nm und ClrSysMmbId und PstlAdr) nicht erlaubt. (R16)", "type": "condition", "conditions": [{"field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI", "type": "present", "value": true}], "rules": [{"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(Nm and ClrSysMmbId and PstlAdr) is prohibited if GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(Nm und ClrSysMmbId und PstlAdr) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(ClrSysMmbId and Nm and PstlAdr) is prohibited if GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(ClrSysMmbId und Nm und PstlAdr) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(ClrSysMmbId and Nm and PstlAdr) is prohibited if GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(ClrSysMmbId und Nm und PstlAdr) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__4625bcb+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}], "rulesConnector": "and"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05", "description": "If GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI is set, then GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(Nm and ClrSysMmbId and PstlAdr) is prohibited. (R16)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist, dann ist GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(Nm und ClrSysMmbId und PstlAdr) nicht erlaubt. (R16)", "type": "condition", "conditions": [{"field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI", "type": "present", "value": true}], "rules": [{"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm__prohibited-conditional", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(Nm and ClrSysMmbId and PstlAdr) is prohibited if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(Nm und ClrSysMmbId und PstlAdr) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__prohibited-conditional", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(ClrSysMmbId and Nm and PstlAdr) is prohibited if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(ClrSysMmbId und Nm und PstlAdr) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId__prohibited-conditional", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(ClrSysMmbId and Nm and PstlAdr) is prohibited if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(ClrSysMmbId und Nm und PstlAdr) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept__prohibited-conditional", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept__prohibited-conditional", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb__prohibited-conditional", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr__prohibited-conditional", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx__prohibited-conditional", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room__prohibited-conditional", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd__prohibited-conditional", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn__prohibited-conditional", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__prohibited-conditional", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"id": "R16__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-[Nm_ClrSysMmbId_PstlAdr]__conditional-prohibited__6426f05+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__prohibited-conditional", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr and Nm and ClrSysMmbId) is prohibited if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI is set. (R16)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(PstlAdr und Nm und ClrSysMmbId) ist nicht erlaubt, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI gesetzt ist. (R16)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}], "rulesConnector": "and"}, {"id": "R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI__conditional-prohibited__12ab2446", "description": "If any of the fields GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(ClrSysMmbId or PstlAdr or Nm) is present, then GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI is prohibited. (R17, R18)", "descriptionTranslationProposal": "Wenn mindestens eines der Felder GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(ClrSysMmbId oder PstlAdr oder Nm) vorhanden ist, dann ist GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI nicht erlaubt. (R17, R18)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm"}], "conditionsConnector": "or", "rules": [{"id": "R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI__conditional-prohibited__12ab2446+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI is prohibited if any of the fields GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(ClrSysMmbId or PstlAdr or Nm) is present. (R17, R18)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI ist nicht erlaubt, wenn mindestens eines der Felder GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(ClrSysMmbId oder PstlAdr oder Nm) vorhanden ist. (R17, R18)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI"}]}, {"id": "R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI__conditional-prohibited__fc1f47e6", "description": "If any of the fields GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(ClrSysMmbId or PstlAdr or Nm) is present, then GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI is prohibited. (R17, R18)", "descriptionTranslationProposal": "Wenn mindestens eines der Felder GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(ClrSysMmbId oder PstlAdr oder Nm) vorhanden ist, dann ist GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI nicht erlaubt. (R17, R18)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm"}], "conditionsConnector": "or", "rules": [{"id": "R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI__conditional-prohibited__fc1f47e6+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI__prohibited-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI is prohibited if any of the fields GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(ClrSysMmbId or PstlAdr or Nm) is present. (R17, R18)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI ist nicht erlaubt, wenn mindestens eines der Felder GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(ClrSysMmbId oder PstlAdr oder Nm) vorhanden ist. (R17, R18)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI"}]}, {"id": "R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI__conditional-prohibited__8f9a7886", "description": "If any of the fields GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(ClrSysMmbId or PstlAdr or Nm) is present, then GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI is prohibited. (R17, R18)", "descriptionTranslationProposal": "Wenn mindestens eines der Felder GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(ClrSysMmbId oder PstlAdr oder Nm) vorhanden ist, dann ist GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI nicht erlaubt. (R17, R18)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm"}], "conditionsConnector": "or", "rules": [{"id": "R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI__conditional-prohibited__8f9a7886+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI__prohibited-conditional", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI is prohibited if any of the fields GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(ClrSysMmbId or PstlAdr or Nm) is present. (R17, R18)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI ist nicht erlaubt, wenn mindestens eines der Felder GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-(ClrSysMmbId oder PstlAdr oder Nm) vorhanden ist. (R17, R18)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI"}]}, {"id": "R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__conditional-required__23f906a0", "description": "If FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm is present, then FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry must also be present. (R17, R18)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm gesetzt ist, dann ist GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-<PERSON><PERSON> erforderlich. (R17, R18)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}], "rules": [{"id": "R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__conditional-required__23f906a0+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__required-conditional", "description": "If FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm is present, then FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry must also be present. (R17, R18)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry ist erford<PERSON>lich, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm gesetzt ist. (R17, R18)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}], "rulesConnector": "and"}, {"id": "R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__981175ec", "description": "If GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm is set, then GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine or TwnNm) is required. (R17, R18)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm gesetzt ist, dann ist GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine oder TwnNm) erforderlich. (R17, R18)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm"}], "rules": [{"id": "R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__981175ec+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__required-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine or TwnNm) is required if GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm is set. (R17, R18)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine oder TwnNm) ist erforderlich, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm gesetzt ist. (R17, R18)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"id": "R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__981175ec+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__required-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm or AdrLine) is required if GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm is set. (R17, R18)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm oder AdrLine) ist erforderlich, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm gesetzt ist. (R17, R18)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}], "rulesConnector": "or"}, {"id": "R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__conditional-required__cc574323", "description": "If FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm is present, then FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry must also be present. (R17, R18)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm gesetzt ist, dann ist GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-<PERSON><PERSON> erforderlich. (R17, R18)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}], "rules": [{"id": "R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__conditional-required__cc574323+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__required-conditional", "description": "If FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm is present, then FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry must also be present. (R17, R18)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry ist erford<PERSON>lich, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm gesetzt ist. (R17, R18)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}], "rulesConnector": "and"}, {"id": "R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__d792e44f", "description": "If GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm is set, then GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine or TwnNm) is required. (R17, R18)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm gesetzt ist, dann ist GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine oder TwnNm) erforderlich. (R17, R18)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm"}], "rules": [{"id": "R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__d792e44f+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__required-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine or TwnNm) is required if GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm is set. (R17, R18)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine oder TwnNm) ist erforderlich, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm gesetzt ist. (R17, R18)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"id": "R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__d792e44f+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__required-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm or AdrLine) is required if GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm is set. (R17, R18)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm oder AdrLine) ist erforderlich, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm gesetzt ist. (R17, R18)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}], "rulesConnector": "or"}, {"id": "R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__conditional-required__75b0636d", "description": "If FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm is present, then FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry must also be present. (R17, R18)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm gesetzt ist, dann ist GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-<PERSON><PERSON> erforderlich. (R17, R18)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}], "rules": [{"id": "R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__conditional-required__75b0636d+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__required-conditional", "description": "If FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm is present, then FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry must also be present. (R17, R18)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry ist erford<PERSON>lich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm gesetzt ist. (R17, R18)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}], "rulesConnector": "and"}, {"id": "R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__5c7c5041", "description": "If GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm is set, then GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine or TwnNm) is required. (R17, R18)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm gesetzt ist, dann ist GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine oder TwnNm) erforderlich. (R17, R18)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm"}], "rules": [{"id": "R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__5c7c5041+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__required-conditional", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine or TwnNm) is required if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm is set. (R17, R18)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine oder TwnNm) ist erforderlich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm gesetzt ist. (R17, R18)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"id": "R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__5c7c5041+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__required-conditional", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm or AdrLine) is required if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm is set. (R17, R18)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm oder AdrLine) ist erforderlich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm gesetzt ist. (R17, R18)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}], "rulesConnector": "or"}, {"id": "R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm__conditional-required__e5151dbf", "description": "If GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr is present, then GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm is required. (R17, R18)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr vorhanden ist, dann ist GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm erforderlich. (R17, R18)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}], "rules": [{"id": "R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm__conditional-required__e5151dbf+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm__required-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm is required if GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr is present. (R17, R18)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm ist erford<PERSON>lich, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr vorhanden ist. (R17, R18)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm"}], "conditionsConnector": "or"}, {"id": "R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm__conditional-required__50f58b9c", "description": "If GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr is present, then GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm is required. (R17, R18)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr vorhanden ist, dann ist GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm erforderlich. (R17, R18)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}], "rules": [{"id": "R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm__conditional-required__50f58b9c+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm__required-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm is required if GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr is present. (R17, R18)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm ist erford<PERSON>lich, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr vorhanden ist. (R17, R18)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm"}], "conditionsConnector": "or"}, {"id": "R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm__conditional-required__6866b32", "description": "If GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr is present, then GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm is required. (R17, R18)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr vorhanden ist, dann ist GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm erforderlich. (R17, R18)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}], "rules": [{"id": "R17-R18__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm__conditional-required__6866b32+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm__required-conditional", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm is required if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr is present. (R17, R18)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm ist erford<PERSON>lich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr vorhanden ist. (R17, R18)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm"}], "conditionsConnector": "or"}, {"id": "R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__conditional-required__23f906a0", "description": "If FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm is present, then FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry must also be present. (R20, R25, R29)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm gesetzt ist, dann ist GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-<PERSON><PERSON> erforderlich. (R20, R25, R29)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}], "rules": [{"id": "R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__conditional-required__23f906a0+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__required-conditional", "description": "If FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm is present, then FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry must also be present. (R20, R25, R29)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry ist erford<PERSON>lich, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm gesetzt ist. (R20, R25, R29)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}], "rulesConnector": "and"}, {"id": "R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__981175ec", "description": "If GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm is set, then GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine or TwnNm) is required. (R20, R25, R29)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm gesetzt ist, dann ist GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine oder TwnNm) erforderlich. (R20, R25, R29)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm"}], "rules": [{"id": "R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__981175ec+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__required-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine or TwnNm) is required if GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm is set. (R20, R25, R29)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine oder TwnNm) ist erforderlich, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm gesetzt ist. (R20, R25, R29)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"id": "R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__981175ec+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__required-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm or AdrLine) is required if GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm is set. (R20, R25, R29)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm oder AdrLine) ist erforderlich, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm gesetzt ist. (R20, R25, R29)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}], "rulesConnector": "or"}, {"id": "R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__conditional-required__cc574323", "description": "If FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm is present, then FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry must also be present. (R20, R25, R29)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm gesetzt ist, dann ist GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-<PERSON><PERSON> erforderlich. (R20, R25, R29)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}], "rules": [{"id": "R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__conditional-required__cc574323+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__required-conditional", "description": "If FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm is present, then FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry must also be present. (R20, R25, R29)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry ist erford<PERSON>lich, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm gesetzt ist. (R20, R25, R29)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}], "rulesConnector": "and"}, {"id": "R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__d792e44f", "description": "If GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm is set, then GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine or TwnNm) is required. (R20, R25, R29)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm gesetzt ist, dann ist GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine oder TwnNm) erforderlich. (R20, R25, R29)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm"}], "rules": [{"id": "R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__d792e44f+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__required-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine or TwnNm) is required if GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm is set. (R20, R25, R29)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine oder TwnNm) ist erforderlich, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm gesetzt ist. (R20, R25, R29)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"id": "R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__d792e44f+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__required-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm or AdrLine) is required if GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm is set. (R20, R25, R29)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm oder AdrLine) ist erforderlich, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm gesetzt ist. (R20, R25, R29)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}], "rulesConnector": "or"}, {"id": "R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__conditional-required__75b0636d", "description": "If FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm is present, then FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry must also be present. (R20, R25, R29)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm gesetzt ist, dann ist GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-<PERSON><PERSON> erforderlich. (R20, R25, R29)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}], "rules": [{"id": "R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__conditional-required__75b0636d+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__required-conditional", "description": "If FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm is present, then FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry must also be present. (R20, R25, R29)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry ist erford<PERSON>lich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm gesetzt ist. (R20, R25, R29)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}], "rulesConnector": "and"}, {"id": "R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__5c7c5041", "description": "If GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm is set, then GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine or TwnNm) is required. (R20, R25, R29)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm gesetzt ist, dann ist GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine oder TwnNm) erforderlich. (R20, R25, R29)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm"}], "rules": [{"id": "R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__5c7c5041+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__required-conditional", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine or TwnNm) is required if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm is set. (R20, R25, R29)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(AdrLine oder TwnNm) ist erforderlich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm gesetzt ist. (R20, R25, R29)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"id": "R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-[AdrLine_TwnNm]__conditional-required__5c7c5041+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__required-conditional", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm or AdrLine) is required if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm is set. (R20, R25, R29)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm oder AdrLine) ist erforderlich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm gesetzt ist. (R20, R25, R29)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}], "rulesConnector": "or"}, {"id": "R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm__conditional-required__e5151dbf", "description": "If GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr is present, then GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm is required. (R20, R25, R29)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr vorhanden ist, dann ist GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm erforderlich. (R20, R25, R29)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}], "rules": [{"id": "R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm__conditional-required__e5151dbf+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm__required-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm is required if GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr is present. (R20, R25, R29)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm ist erford<PERSON>lich, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr vorhanden ist. (R20, R25, R29)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm"}], "conditionsConnector": "or"}, {"id": "R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm__conditional-required__50f58b9c", "description": "If GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr is present, then GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm is required. (R20, R25, R29)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr vorhanden ist, dann ist GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm erforderlich. (R20, R25, R29)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}], "rules": [{"id": "R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm__conditional-required__50f58b9c+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm__required-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm is required if GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr is present. (R20, R25, R29)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm ist erford<PERSON>lich, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr vorhanden ist. (R20, R25, R29)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm"}], "conditionsConnector": "or"}, {"id": "R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm__conditional-required__6866b32", "description": "If GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr is present, then GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm is required. (R20, R25, R29)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr vorhanden ist, dann ist GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm erforderlich. (R20, R25, R29)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}], "rules": [{"id": "R20-R25-R29__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm__conditional-required__6866b32+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm__required-conditional", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm is required if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr is present. (R20, R25, R29)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm ist erford<PERSON>lich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr vorhanden ist. (R20, R25, R29)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm"}], "conditionsConnector": "or"}, {"id": "R22-R27-R31__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-[TwnNm_Ctry]__conditional-required__e92a3b8d", "description": "If GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine is set and any of the fields GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(Dept or SubDept or StrtNm or BldgNb or BldgNm or Flr or PstBx or Room or PstCd or TwnLctnNm or DstrctNm or CtrySubDvsn) is present, then GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm and Ctry) is required. (R22, R27, R31)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine gesetzt ist and mindestens eines der Felder GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn) vorhanden ist, dann ist GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm und Ctry) erforderlich. (R22, R27, R31)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}], "conditionsConnector": "or"}], "conditionsConnector": "and", "rules": [{"id": "R22-R27-R31__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-[TwnNm_Ctry]__conditional-required__e92a3b8d+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__required-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm and Ctry) is required if GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine is set and any of the fields GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(Dept or SubDept or StrtNm or BldgNb or BldgNm or Flr or PstBx or Room or PstCd or TwnLctnNm or DstrctNm or CtrySubDvsn) is present. (R22, R27, R31)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm und Ctry) ist erforderlich, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine gesetzt ist and mindestens eines der Felder GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn) vorhanden ist. (R22, R27, R31)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"id": "R22-R27-R31__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-[TwnNm_Ctry]__conditional-required__e92a3b8d+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__required-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(Ctry and TwnNm) is required if GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine is set and any of the fields GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(Dept or SubDept or StrtNm or BldgNb or BldgNm or Flr or PstBx or Room or PstCd or TwnLctnNm or DstrctNm or CtrySubDvsn) is present. (R22, R27, R31)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(Ctry und TwnNm) ist erforderlich, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine gesetzt ist and mindestens eines der Felder GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn) vorhanden ist. (R22, R27, R31)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}]}, {"id": "R22-R27-R31__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-[TwnNm_Ctry]__conditional-required__e0cb6bee", "description": "If GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine is set and any of the fields GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept or SubDept or StrtNm or BldgNb or BldgNm or Flr or PstBx or Room or PstCd or TwnLctnNm or DstrctNm or CtrySubDvsn) is present, then GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm and Ctry) is required. (R22, R27, R31)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine gesetzt ist and mindestens eines der Felder GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn) vorhanden ist, dann ist GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm und Ctry) erforderlich. (R22, R27, R31)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}], "conditionsConnector": "or"}], "conditionsConnector": "and", "rules": [{"id": "R22-R27-R31__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-[TwnNm_Ctry]__conditional-required__e0cb6bee+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__required-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm and Ctry) is required if GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine is set and any of the fields GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept or SubDept or StrtNm or BldgNb or BldgNm or Flr or PstBx or Room or PstCd or TwnLctnNm or DstrctNm or CtrySubDvsn) is present. (R22, R27, R31)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm und Ctry) ist erforderlich, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine gesetzt ist and mindestens eines der Felder GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn) vorhanden ist. (R22, R27, R31)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"id": "R22-R27-R31__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-[TwnNm_Ctry]__conditional-required__e0cb6bee+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__required-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(Ctry and TwnNm) is required if GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine is set and any of the fields GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept or SubDept or StrtNm or BldgNb or BldgNm or Flr or PstBx or Room or PstCd or TwnLctnNm or DstrctNm or CtrySubDvsn) is present. (R22, R27, R31)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(Ctry und TwnNm) ist erforderlich, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine gesetzt ist and mindestens eines der Felder GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn) vorhanden ist. (R22, R27, R31)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}]}, {"id": "R22-R27-R31__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-[TwnNm_Ctry]__conditional-required__7c3a0c20", "description": "If GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine is set and any of the fields GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept or SubDept or StrtNm or BldgNb or BldgNm or Flr or PstBx or Room or PstCd or TwnLctnNm or DstrctNm or CtrySubDvsn) is present, then GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm and Ctry) is required. (R22, R27, R31)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine gesetzt ist and mindestens eines der Felder GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn) vorhanden ist, dann ist GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm und Ctry) erforderlich. (R22, R27, R31)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}], "conditionsConnector": "or"}], "conditionsConnector": "and", "rules": [{"id": "R22-R27-R31__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-[TwnNm_Ctry]__conditional-required__7c3a0c20+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__required-conditional", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm and Ctry) is required if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine is set and any of the fields GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept or SubDept or StrtNm or BldgNb or BldgNm or Flr or PstBx or Room or PstCd or TwnLctnNm or DstrctNm or CtrySubDvsn) is present. (R22, R27, R31)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(TwnNm und Ctry) ist erforderlich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Adr<PERSON>ine gesetzt ist and mindestens eines der Felder GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn) vorhanden ist. (R22, R27, R31)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"id": "R22-R27-R31__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-[TwnNm_Ctry]__conditional-required__7c3a0c20+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__required-conditional", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(Ctry and TwnNm) is required if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine is set and any of the fields GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept or SubDept or StrtNm or BldgNb or BldgNm or Flr or PstBx or Room or PstCd or TwnLctnNm or DstrctNm or CtrySubDvsn) is present. (R22, R27, R31)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(Ctry und TwnNm) ist erforderlich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Adr<PERSON>ine gesetzt ist and mindestens eines der Felder GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn) vorhanden ist. (R22, R27, R31)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}]}, {"id": "R22-R27-R31__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__conditional-maxItems__e92a3b8d", "description": "If GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine is set and any of the fields GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(Dept or SubDept or StrtNm or BldgNb or BldgNm or Flr or PstBx or Room or PstCd or TwnLctnNm or DstrctNm or CtrySubDvsn) is present, then GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine must have at most 2 items. (R22, R27, R31)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine gesetzt ist and mindestens eines der Felder GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn) vorhanden ist, dann darf GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine höchstens 2 Elemente haben. (R22, R27, R31)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}], "conditionsConnector": "or"}], "conditionsConnector": "and", "rules": [{"id": "R22-R27-R31__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__conditional-maxItems__e92a3b8d+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__maxItems-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine must have at most 2 items if GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine is set and any of the fields GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(Dept or SubDept or StrtNm or BldgNb or BldgNm or Flr or PstBx or Room or PstCd or TwnLctnNm or DstrctNm or CtrySubDvsn) is present. (R22, R27, R31)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine darf höchstens 2 Elemente haben, wenn GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine gesetzt ist and mindestens eines der Felder GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn) vorhanden ist. (R22, R27, R31)", "type": "maxItems", "value": 2, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}]}, {"id": "R22-R27-R31__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__conditional-maxItems__e0cb6bee", "description": "If GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine is set and any of the fields GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept or SubDept or StrtNm or BldgNb or BldgNm or Flr or PstBx or Room or PstCd or TwnLctnNm or DstrctNm or CtrySubDvsn) is present, then GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine must have at most 2 items. (R22, R27, R31)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine gesetzt ist and mindestens eines der Felder GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn) vorhanden ist, dann darf GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine höchstens 2 Elemente haben. (R22, R27, R31)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}], "conditionsConnector": "or"}], "conditionsConnector": "and", "rules": [{"id": "R22-R27-R31__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__conditional-maxItems__e0cb6bee+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__maxItems-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine must have at most 2 items if GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine is set and any of the fields GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept or SubDept or StrtNm or BldgNb or BldgNm or Flr or PstBx or Room or PstCd or TwnLctnNm or DstrctNm or CtrySubDvsn) is present. (R22, R27, R31)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine darf höchstens 2 Elemente haben, wenn GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine gesetzt ist and mindestens eines der Felder GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn) vorhanden ist. (R22, R27, R31)", "type": "maxItems", "value": 2, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}]}, {"id": "R22-R27-R31__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__conditional-maxItems__7c3a0c20", "description": "If GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine is set and any of the fields GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept or SubDept or StrtNm or BldgNb or BldgNm or Flr or PstBx or Room or PstCd or TwnLctnNm or DstrctNm or CtrySubDvsn) is present, then GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine must have at most 2 items. (R22, R27, R31)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine gesetzt ist and mindestens eines der Felder GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn) vorhanden ist, dann darf GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine höchstens 2 Elemente haben. (R22, R27, R31)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}], "conditionsConnector": "or"}], "conditionsConnector": "and", "rules": [{"id": "R22-R27-R31__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__conditional-maxItems__7c3a0c20+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__maxItems-conditional", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine must have at most 2 items if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine is set and any of the fields GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept or SubDept or StrtNm or BldgNb or BldgNm or Flr or PstBx or Room or PstCd or TwnLctnNm or DstrctNm or CtrySubDvsn) is present. (R22, R27, R31)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine darf höchstens 2 Elemente haben, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine gesetzt ist and mindestens eines der Felder GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnLctnNm oder DstrctNm oder CtrySubDvsn) vorhanden ist. (R22, R27, R31)", "type": "maxItems", "value": 2, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}]}, {"id": "R23__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__contains", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine must not repeat data present in FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(Dept or SubDept or StrtNm or BldgNb or BldgNm or Flr or PstBx or Room or PstCd or TwnNm or TwnLctnNm or DstrctNm or CtrySubDvsn or Ctry). (R23)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ad<PERSON><PERSON><PERSON> darf Daten, die in FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnNm oder TwnLctnNm oder DstrctNm oder CtrySubDvsn oder Ctry) vorhanden sind, nicht wiederholen. (R23)", "type": "contains", "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine", "value": ["FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"], "contains": false}, {"id": "R23__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__contains", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine must not repeat data present in FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept or SubDept or StrtNm or BldgNb or BldgNm or Flr or PstBx or Room or PstCd or TwnNm or TwnLctnNm or DstrctNm or CtrySubDvsn or Ctry). (R23)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Adr<PERSON><PERSON> darf Daten, die in FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnNm oder TwnLctnNm oder DstrctNm oder CtrySubDvsn oder Ctry) vorhanden sind, nicht wiederholen. (R23)", "type": "contains", "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine", "value": ["FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"], "contains": false}, {"id": "R23__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__contains", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine must not repeat data present in FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept or SubDept or StrtNm or BldgNb or BldgNm or Flr or PstBx or Room or PstCd or TwnNm or TwnLctnNm or DstrctNm or CtrySubDvsn or Ctry). (R23)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Adr<PERSON><PERSON> darf Daten, die in FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept oder SubDept oder StrtNm oder BldgNb oder BldgNm oder Flr oder PstBx oder Room oder PstCd oder TwnNm oder TwnLctnNm oder DstrctNm oder CtrySubDvsn oder Ctry) vorhanden sind, nicht wiederholen. (R23)", "type": "contains", "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine", "value": ["FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn", "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"], "contains": false}, {"id": "R24-R28-R32__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__conditional-maxLength__a2be53f1", "description": "If none of the fields GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(Dept and SubDept and StrtNm and BldgNb and BldgNm and Flr and PstBx and Room and PstCd and TwnNm and TwnLctnNm and DstrctNm and CtrySubDvsn and Ctry) is present, then GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine must be at most 35 characters long. (R24, R28, R32)", "descriptionTranslationProposal": "Wenn keines der Felder GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(Dept und SubDept und StrtNm und BldgNb und BldgNm und Flr und PstBx und Room und PstCd und TwnNm und TwnLctnNm und DstrctNm und CtrySubDvsn und Ctry) vorhanden ist, dann darf GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine höchstens 35 Zeichen lang sein. (R24, R28, R32)", "type": "condition", "conditions": [{"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}], "rules": [{"id": "R24-R28-R32__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__conditional-maxLength__a2be53f1+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__maxLength-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine must be at most 35 characters long if none of the fields GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(Dept and SubDept and StrtNm and BldgNb and BldgNm and Flr and PstBx and Room and PstCd and TwnNm and TwnLctnNm and DstrctNm and CtrySubDvsn and Ctry) is present. (R24, R28, R32)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Adr<PERSON><PERSON> darf höchstens 35 Zeichen lang sein, wenn keines der Felder GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-(Dept und SubDept und StrtNm und BldgNb und BldgNm und Flr und PstBx und Room und PstCd und TwnNm und TwnLctnNm und DstrctNm und CtrySubDvsn und Ctry) vorhanden ist. (R24, R28, R32)", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}]}, {"id": "R24-R28-R32__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__conditional-maxLength__17579e71", "description": "If none of the fields GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept and SubDept and StrtNm and BldgNb and BldgNm and Flr and PstBx and Room and PstCd and TwnNm and TwnLctnNm and DstrctNm and CtrySubDvsn and Ctry) is present, then GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine must be at most 35 characters long. (R24, R28, R32)", "descriptionTranslationProposal": "Wenn keines der Felder GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept und SubDept und StrtNm und BldgNb und BldgNm und Flr und PstBx und Room und PstCd und TwnNm und TwnLctnNm und DstrctNm und CtrySubDvsn und Ctry) vorhanden ist, dann darf GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine höchstens 35 Zeichen lang sein. (R24, R28, R32)", "type": "condition", "conditions": [{"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}], "rules": [{"id": "R24-R28-R32__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__conditional-maxLength__17579e71+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__maxLength-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine must be at most 35 characters long if none of the fields GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept and SubDept and StrtNm and BldgNb and BldgNm and Flr and PstBx and Room and PstCd and TwnNm and TwnLctnNm and DstrctNm and CtrySubDvsn and Ctry) is present. (R24, R28, R32)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Adr<PERSON><PERSON> darf höchstens 35 Zeichen lang sein, wenn keines der Felder GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept und SubDept und StrtNm und BldgNb und BldgNm und Flr und PstBx und Room und PstCd und TwnNm und TwnLctnNm und DstrctNm und CtrySubDvsn und Ctry) vorhanden ist. (R24, R28, R32)", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}]}, {"id": "R24-R28-R32__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__conditional-maxLength__2ae6dcd1", "description": "If none of the fields GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept and SubDept and StrtNm and BldgNb and BldgNm and Flr and PstBx and Room and PstCd and TwnNm and TwnLctnNm and DstrctNm and CtrySubDvsn and Ctry) is present, then GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine must be at most 35 characters long. (R24, R28, R32)", "descriptionTranslationProposal": "Wenn keines der Felder GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept und SubDept und StrtNm und BldgNb und BldgNm und Flr und PstBx und Room und PstCd und TwnNm und TwnLctnNm und DstrctNm und CtrySubDvsn und Ctry) vorhanden ist, dann darf GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine höchstens 35 Zeichen lang sein. (R24, R28, R32)", "type": "condition", "conditions": [{"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}], "rules": [{"id": "R24-R28-R32__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__conditional-maxLength__2ae6dcd1+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__maxLength-conditional", "description": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine must be at most 35 characters long if none of the fields GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept and SubDept and StrtNm and BldgNb and BldgNm and Flr and PstBx and Room and PstCd and TwnNm and TwnLctnNm and DstrctNm and CtrySubDvsn and Ctry) is present. (R24, R28, R32)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Adr<PERSON><PERSON> darf höchstens 35 Zeichen lang sein, wenn keines der Felder GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-(Dept und SubDept und StrtNm und BldgNb und BldgNm und Flr und PstBx und Room und PstCd und TwnNm und TwnLctnNm und DstrctNm und CtrySubDvsn und Ctry) vorhanden ist. (R24, R28, R32)", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}]}, {"id": "ThirdReimbursementAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[BICFI_Nm]__conditional-required__babd48c1", "description": "If GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId is present, then GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(BICFI or Nm) is required. (ThirdReimbursementAgentRule)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId vorhanden ist, dann ist GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(BICFI oder Nm) erforderlich. (ThirdReimbursementAgentRule)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-LEI"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}], "rules": [{"id": "ThirdReimbursementAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[BICFI_Nm]__conditional-required__babd48c1+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI__required-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(BICFI or Nm) is required if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId is present. (ThirdReimbursementAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(BICFI oder Nm) ist erforderlich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId vorhanden ist. (ThirdReimbursementAgentRule)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI"}, {"id": "ThirdReimbursementAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-[BICFI_Nm]__conditional-required__babd48c1+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm__required-conditional", "description": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(Nm or BICFI) is required if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId is present. (ThirdReimbursementAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-(Nm oder BICFI) ist erforderlich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId vorhanden ist. (ThirdReimbursementAgentRule)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm"}], "rulesConnector": "or", "conditionsConnector": "or"}, {"id": "ThirdReimbursementAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[BICFI_Nm]__conditional-required__babd48c1", "description": "If GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId is present, then GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(BICFI or Nm) is required. (ThirdReimbursementAgentRule)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId vorhanden ist, dann ist GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(BICFI oder Nm) erforderlich. (ThirdReimbursementAgentRule)", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-LEI"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}], "rules": [{"id": "ThirdReimbursementAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[BICFI_Nm]__conditional-required__babd48c1+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI__required-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(BICFI or Nm) is required if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId is present. (ThirdReimbursementAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(BICFI oder Nm) ist erforderlich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId vorhanden ist. (ThirdReimbursementAgentRule)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI"}, {"id": "ThirdReimbursementAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-[BICFI_Nm]__conditional-required__babd48c1+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm__required-conditional", "description": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(Nm or BICFI) is required if GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId is present. (ThirdReimbursementAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-(Nm oder BICFI) ist erforderlich, wenn GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId vorhanden ist. (ThirdReimbursementAgentRule)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm"}], "rulesConnector": "or", "conditionsConnector": "or"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79", "description": "If GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\", then GrpHdr-SttlmInf-(InstgRmbrsmntAgt and InstdRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited. (SettlementMethodAgentRule)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat, dann ist GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) nicht erlaubt. (SettlementMethodAgentRule)", "type": "condition", "conditions": [{"type": "value", "value": "INDA", "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmMtd"}, {"type": "value", "value": "INGA", "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmMtd"}], "conditionsConnector": "or", "rules": [{"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt and InstdRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt and InstdRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt and InstdRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-LEI__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt and InstdRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-LEI"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt and InstdRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt and InstdRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt and InstdRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt and InstdRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt and InstdRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt and InstdRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt and InstdRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt and InstdRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt and InstdRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt and InstdRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt and InstdRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt and InstdRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt and InstdRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt and InstdRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt and InstdRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt and InstdRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt und InstdRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt and InstgRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt and InstgRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt and InstgRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-LEI__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt and InstgRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-LEI"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt and InstgRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt and InstgRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt and InstgRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt and InstgRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt and InstgRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt and InstgRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt and InstgRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt and InstgRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt and InstgRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt and InstgRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt and InstgRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt and InstgRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt and InstgRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt and InstgRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt and InstgRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__prohibited-conditional", "description": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt and InstgRmbrsmntAgt and ThrdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt und InstgRmbrsmntAgt und ThrdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI__prohibited-conditional", "description": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt and InstgRmbrsmntAgt and InstdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd__prohibited-conditional", "description": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt and InstgRmbrsmntAgt and InstdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId__prohibited-conditional", "description": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt and InstgRmbrsmntAgt and InstdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-LEI__prohibited-conditional", "description": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt and InstgRmbrsmntAgt and InstdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-LEI"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm__prohibited-conditional", "description": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt and InstgRmbrsmntAgt and InstdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept__prohibited-conditional", "description": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt and InstgRmbrsmntAgt and InstdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept__prohibited-conditional", "description": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt and InstgRmbrsmntAgt and InstdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt and InstgRmbrsmntAgt and InstdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb__prohibited-conditional", "description": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt and InstgRmbrsmntAgt and InstdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt and InstgRmbrsmntAgt and InstdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr__prohibited-conditional", "description": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt and InstgRmbrsmntAgt and InstdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx__prohibited-conditional", "description": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt and InstgRmbrsmntAgt and InstdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room__prohibited-conditional", "description": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt and InstgRmbrsmntAgt and InstdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd__prohibited-conditional", "description": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt and InstgRmbrsmntAgt and InstdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt and InstgRmbrsmntAgt and InstdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt and InstgRmbrsmntAgt and InstdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm__prohibited-conditional", "description": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt and InstgRmbrsmntAgt and InstdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn__prohibited-conditional", "description": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt and InstgRmbrsmntAgt and InstdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry__prohibited-conditional", "description": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt and InstgRmbrsmntAgt and InstdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"id": "SettlementMethodAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt]__conditional-prohibited__adf4cb79+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine__prohibited-conditional", "description": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt and InstgRmbrsmntAgt and InstdRmbrsmntAgt) is prohibited if GrpHdr-SttlmInf-SttlmMtd has one of the values \"INDA\" or \"INGA\". (SettlementMethodAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(ThrdRmbrsmntAgt und InstgRmbrsmntAgt und InstdRmbrsmntAgt) ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd einen der Werte \"INDA\" oder \"INGA\" hat. (SettlementMethodAgentRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}], "rulesConnector": "and"}, {"id": "SettlementMethodCoverRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct__conditional-prohibited__24fcd5dc", "description": "If GrpHdr-SttlmInf-SttlmMtd has the value \"COVE\", then GrpHdr-SttlmInf-SttlmAcct is prohibited. (SettlementMethodCoverRule)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat, dann ist GrpHdr-SttlmInf-SttlmAcct nicht erlaubt. (SettlementMethodCoverRule)", "type": "condition", "conditions": [{"type": "value", "value": "COVE", "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmMtd"}], "rules": [{"id": "SettlementMethodCoverRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct__conditional-prohibited__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-IBAN__prohibited-conditional", "description": "GrpHdr-SttlmInf-SttlmAcct is prohibited if GrpHdr-SttlmInf-SttlmMtd has the value \"COVE\". (SettlementMethodCoverRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-IBAN"}, {"id": "SettlementMethodCoverRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct__conditional-prohibited__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id__prohibited-conditional", "description": "GrpHdr-SttlmInf-SttlmAcct is prohibited if GrpHdr-SttlmInf-SttlmMtd has the value \"COVE\". (SettlementMethodCoverRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id"}, {"id": "SettlementMethodCoverRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct__conditional-prohibited__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd__prohibited-conditional", "description": "GrpHdr-SttlmInf-SttlmAcct is prohibited if GrpHdr-SttlmInf-SttlmMtd has the value \"COVE\". (SettlementMethodCoverRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd"}, {"id": "SettlementMethodCoverRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct__conditional-prohibited__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry__prohibited-conditional", "description": "GrpHdr-SttlmInf-SttlmAcct is prohibited if GrpHdr-SttlmInf-SttlmMtd has the value \"COVE\". (SettlementMethodCoverRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry"}, {"id": "SettlementMethodCoverRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct__conditional-prohibited__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr__prohibited-conditional", "description": "GrpHdr-SttlmInf-SttlmAcct is prohibited if GrpHdr-SttlmInf-SttlmMtd has the value \"COVE\". (SettlementMethodCoverRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr"}, {"id": "SettlementMethodCoverRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct__conditional-prohibited__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Cd__prohibited-conditional", "description": "GrpHdr-SttlmInf-SttlmAcct is prohibited if GrpHdr-SttlmInf-SttlmMtd has the value \"COVE\". (SettlementMethodCoverRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Cd"}, {"id": "SettlementMethodCoverRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct__conditional-prohibited__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry__prohibited-conditional", "description": "GrpHdr-SttlmInf-SttlmAcct is prohibited if GrpHdr-SttlmInf-SttlmMtd has the value \"COVE\". (SettlementMethodCoverRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry"}, {"id": "SettlementMethodCoverRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct__conditional-prohibited__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Ccy__prohibited-conditional", "description": "GrpHdr-SttlmInf-SttlmAcct is prohibited if GrpHdr-SttlmInf-SttlmMtd has the value \"COVE\". (SettlementMethodCoverRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Ccy"}, {"id": "SettlementMethodCoverRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct__conditional-prohibited__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Nm__prohibited-conditional", "description": "GrpHdr-SttlmInf-SttlmAcct is prohibited if GrpHdr-SttlmInf-SttlmMtd has the value \"COVE\". (SettlementMethodCoverRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Nm"}, {"id": "SettlementMethodCoverRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct__conditional-prohibited__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Cd__prohibited-conditional", "description": "GrpHdr-SttlmInf-SttlmAcct is prohibited if GrpHdr-SttlmInf-SttlmMtd has the value \"COVE\". (SettlementMethodCoverRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Cd"}, {"id": "SettlementMethodCoverRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct__conditional-prohibited__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry__prohibited-conditional", "description": "GrpHdr-SttlmInf-SttlmAcct is prohibited if GrpHdr-SttlmInf-SttlmMtd has the value \"COVE\". (SettlementMethodCoverRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry"}, {"id": "SettlementMethodCoverRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct__conditional-prohibited__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id__prohibited-conditional", "description": "GrpHdr-SttlmInf-SttlmAcct is prohibited if GrpHdr-SttlmInf-SttlmMtd has the value \"COVE\". (SettlementMethodCoverRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-SttlmAcct ist nicht erlaubt, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverRule)", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id"}], "rulesConnector": "and"}, {"id": "SettlementMethodCoverAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstdRmbrsmntAgt-FinInstnId-BICFI_InstdRmbrsmntAgt-FinInstnId-Nm_InstgRmbrsmntAgt-FinInstnId-BICFI_InstgRmbrsmntAgt-FinInstnId-Nm]__conditional-required__24fcd5dc", "description": "If GrpHdr-SttlmInf-SttlmMtd has the value \"COVE\", then GrpHdr-SttlmInf-(InstdRmbrsmntAgt-FinInstnId-BICFI or InstdRmbrsmntAgt-FinInstnId-Nm or InstgRmbrsmntAgt-FinInstnId-BICFI or InstgRmbrsmntAgt-FinInstnId-Nm) is required. (SettlementMethodCoverAgentRule)", "descriptionTranslationProposal": "Wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat, dann ist GrpHdr-SttlmInf-(InstdRmbrsmntAgt-FinInstnId-BICFI oder InstdRmbrsmntAgt-FinInstnId-Nm oder InstgRmbrsmntAgt-FinInstnId-BICFI oder InstgRmbrsmntAgt-FinInstnId-Nm) erforderlich. (SettlementMethodCoverAgentRule)", "type": "condition", "conditions": [{"type": "value", "value": "COVE", "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmMtd"}], "rules": [{"id": "SettlementMethodCoverAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstdRmbrsmntAgt-FinInstnId-BICFI_InstdRmbrsmntAgt-FinInstnId-Nm_InstgRmbrsmntAgt-FinInstnId-BICFI_InstgRmbrsmntAgt-FinInstnId-Nm]__conditional-required__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI__required-conditional", "description": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt-FinInstnId-BICFI or InstdRmbrsmntAgt-FinInstnId-Nm or InstgRmbrsmntAgt-FinInstnId-BICFI or InstgRmbrsmntAgt-FinInstnId-Nm) is required if GrpHdr-SttlmInf-SttlmMtd has the value \"COVE\". (SettlementMethodCoverAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt-FinInstnId-BICFI oder InstdRmbrsmntAgt-FinInstnId-Nm oder InstgRmbrsmntAgt-FinInstnId-BICFI oder InstgRmbrsmntAgt-FinInstnId-Nm) ist erforderlich, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverAgentRule)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI"}, {"id": "SettlementMethodCoverAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstdRmbrsmntAgt-FinInstnId-BICFI_InstdRmbrsmntAgt-FinInstnId-Nm_InstgRmbrsmntAgt-FinInstnId-BICFI_InstgRmbrsmntAgt-FinInstnId-Nm]__conditional-required__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm__required-conditional", "description": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt-FinInstnId-Nm or InstdRmbrsmntAgt-FinInstnId-BICFI or InstgRmbrsmntAgt-FinInstnId-BICFI or InstgRmbrsmntAgt-FinInstnId-Nm) is required if GrpHdr-SttlmInf-SttlmMtd has the value \"COVE\". (SettlementMethodCoverAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstdRmbrsmntAgt-FinInstnId-Nm oder InstdRmbrsmntAgt-FinInstnId-BICFI oder InstgRmbrsmntAgt-FinInstnId-BICFI oder InstgRmbrsmntAgt-FinInstnId-Nm) ist erforderlich, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverAgentRule)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm"}, {"id": "SettlementMethodCoverAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstdRmbrsmntAgt-FinInstnId-BICFI_InstdRmbrsmntAgt-FinInstnId-Nm_InstgRmbrsmntAgt-FinInstnId-BICFI_InstgRmbrsmntAgt-FinInstnId-Nm]__conditional-required__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI__required-conditional", "description": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt-FinInstnId-BICFI or InstdRmbrsmntAgt-FinInstnId-BICFI or InstdRmbrsmntAgt-FinInstnId-Nm or InstgRmbrsmntAgt-FinInstnId-Nm) is required if GrpHdr-SttlmInf-SttlmMtd has the value \"COVE\". (SettlementMethodCoverAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt-FinInstnId-BICFI oder InstdRmbrsmntAgt-FinInstnId-BICFI oder InstdRmbrsmntAgt-FinInstnId-Nm oder InstgRmbrsmntAgt-FinInstnId-Nm) ist erforderlich, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverAgentRule)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI"}, {"id": "SettlementMethodCoverAgentRule__FIToFICstmrCdtTrf-GrpHdr-SttlmInf-[InstdRmbrsmntAgt-FinInstnId-BICFI_InstdRmbrsmntAgt-FinInstnId-Nm_InstgRmbrsmntAgt-FinInstnId-BICFI_InstgRmbrsmntAgt-FinInstnId-Nm]__conditional-required__24fcd5dc+FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm__required-conditional", "description": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt-FinInstnId-Nm or InstdRmbrsmntAgt-FinInstnId-BICFI or InstdRmbrsmntAgt-FinInstnId-Nm or InstgRmbrsmntAgt-FinInstnId-BICFI) is required if GrpHdr-SttlmInf-SttlmMtd has the value \"COVE\". (SettlementMethodCoverAgentRule)", "descriptionTranslationProposal": "GrpHdr-SttlmInf-(InstgRmbrsmntAgt-FinInstnId-Nm oder InstdRmbrsmntAgt-FinInstnId-BICFI oder InstdRmbrsmntAgt-FinInstnId-Nm oder InstgRmbrsmntAgt-FinInstnId-BICFI) ist erforderlich, wenn GrpHdr-SttlmInf-SttlmMtd den Wert \"COVE\" hat. (SettlementMethodCoverAgentRule)", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm"}], "rulesConnector": "or"}]