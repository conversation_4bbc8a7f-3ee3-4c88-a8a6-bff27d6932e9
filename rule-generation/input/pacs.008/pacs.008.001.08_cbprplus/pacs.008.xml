<?xml version="1.0" encoding="UTF-8"?>
<Document xmlns="urn:iso:std:iso:20022:tech:xsd:pacs.008.001.08"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="urn:iso:std:iso:20022:tech:xsd:pacs.008.001.08 ./schema.xsd">
    <FIToFICstmrCdtTrf>
        <GrpHdr>
            <MsgId>BANKA-********-123456</MsgId>
            <CreDtTm>2023-04-21T09:30:47.000+02:00</CreDtTm>
            <NbOfTxs>1</NbOfTxs>
            <SttlmInf>
                <SttlmMtd>COVE</SttlmMtd>
                <!-- In the case of COVE: The account at the intermediary bank (nostro account of
                the instructing agent) used for settlement. Often omitted if the creditor agent has
                an established relationship with the intermediary institution and knows where to
                expect the funds. Also, the InstgRmbrsmntAgt element describes the same intermediary bank so
                really, this is omitted almost always. -->
                <!-- Typically not used with CLRG, unless specifying a technical account at the
                clearing system, in that case use ClrSys -->
                <SttlmAcct>
                    <Id>
                        <IBAN>**********************</IBAN> <!-- In the case of COVE: IBAN of the
                        InstgRmbrsmntAgt -->
                    </Id>
                    <Tp> <!-- Type of account used for settlement -->
                        <Cd>CACC</Cd> <!-- Current Account: Standard operating account for daily
                        banking operations -->
                    </Tp>
                    <Ccy>EUR</Ccy>
                    <Nm>Bank A Settlement Account</Nm>
                </SttlmAcct>
                <InstgRmbrsmntAgt>
                    <FinInstnId>
                        <BICFI>RMAAXXXXXXX</BICFI>
                    </FinInstnId>
                </InstgRmbrsmntAgt>
                <InstdRmbrsmntAgt>
                    <FinInstnId>
                        <BICFI>RMABXXXXXXX</BICFI>
                    </FinInstnId>
                </InstdRmbrsmntAgt>
            </SttlmInf>
        </GrpHdr>
        <CdtTrfTxInf>
            <PmtId>
                <InstrId>BANKA-********</InstrId> <!-- Instruction Identification -->
                <EndToEndId>NOTPROVIDED</EndToEndId>
                <UETR>d0b7077f-49fb-42ed-b78d-af331c0e5012</UETR> <!-- UUIDv4 -->
            </PmtId>
            <PmtTpInf>
                <!-- Instruction Priority: Relative processing priority within an institution -->
                <InstrPrty>HIGH</InstrPrty>
                <!-- Clearing Channel: Specifies the clearing channel to be used to process the
                payment. Rarely used in CBPR+, because the routing/clearing info is handled by SWIFT and the
                settlement system (e.g. via BICs). -->
                <!-- <ClrChanl /> -->
                <SvcLvl> <!-- Service Level: rules under which the payment should be processed -->
                    <!-- Indicates service level (including settlement mechanisms, timeframes,
                    processing channels). Usually aligned with InstrPrty -->
                    <Cd>URGP</Cd>
                </SvcLvl>
                <SvcLvl>
                    <!-- Explicitly identifies the payment as a GPI transaction -->
                    <Prtry>G001</Prtry> <!-- Proprietary -->
                </SvcLvl>
                <!-- Further specify the payment product, scheme, or instrument type -->
                <!-- For CBPR+, forbidden according to SWIFT schema and rulebooks -->
                <!-- In SEPA, you'd use values like CORE (SEPA Core Direct Debit), B2B (SEPA
                Business-to-Business Direct Debit), or INST (SEPA Instant Credit Transfer) -->
                <!-- <LclInstrm /> -->
                <!-- Category Purpose: Describes the business reason or high-level purpose of the
                transaction, can trigger special processing, e.g. SALA (Salary Payment) may trigger a reporting
                process which restricts sensitive data. -->
                <CtgyPurp>
                    <Cd>SUPP</Cd> <!-- Supplier payment, could also be SALA (Salary payment), PENS
                    (Pension), TAXS (Tax payment) etc. -->
                </CtgyPurp>
            </PmtTpInf>
            <!-- The debtor wants to pay the creditor 1000 USD, interbank settlement takes place in
            EUR, FX rate of 1 USD = 0.9 EUR is pre-agreed. -->
            <IntrBkSttlmAmt Ccy="EUR">900.00</IntrBkSttlmAmt>
            <IntrBkSttlmDt>2023-04-21</IntrBkSttlmDt>
            <SttlmPrty>HIGH</SttlmPrty> <!-- Settlement Priority, not needed here (only set in the
            pacs.009), InstrPrty is enough -->
            <!-- When did the settlement occur at the settlement agent? -->
            <!-- <SttlmTmIndctn /> -->
            <!-- Request for settlement time -->
            <SttlmTmReq>
                <CLSTm>11:30:00.000+01:00</CLSTm> <!-- Time by which the amount of money must be
                credited,
                with confirmation, to the CLS (Continuous Linked Settlement) Bank's account at the
                central bank. -->
                <TillTm>12:00:00.000+01:00</TillTm>
                <FrTm>11:00:00.000+01:00</FrTm>
                <RjctTm>12:00:00.000+01:00</RjctTm>
            </SttlmTmReq>
            <!-- <AddtlDtTm /> -->
            <InstdAmt Ccy="USD">1000.00</InstdAmt> <!-- Not needed if amount and currency are the
            same as for interbank settlement -->
            <XchgRate>0.9</XchgRate>
            <!-- <AgrdRate /> -->
            <ChrgBr>DEBT</ChrgBr> <!-- Specify which party bears charges associated with processing
            the payment transaction. (CREDitor, DEBTor, SHARed) -->
            <ChrgsInf>
                <Amt Ccy="EUR">5.00</Amt>
                <Agt>
                    <FinInstnId>
                        <BICFI>BANKGBCCXXX</BICFI>
                    </FinInstnId>
                </Agt>
            </ChrgsInf>
            <!-- <MndtRltdInf /> Mandate Related Information (Provides further details of the
            mandate signed between the creditor and the debtor) -->
            <!-- <PmtSgntr /> -->
            <!-- Historic Agents between the debtor agent and the Instructing Agent -->
            <!-- <PrvsInstgAgt1 /> -->
            <!-- <PrvsInstgAgt1Acct /> -->
            <!-- <PrvsInstgAgt2 /> -->
            <!-- <PrvsInstgAgt2Acct /> -->
            <!-- <PrvsInstgAgt3 /> -->
            <!-- <PrvsInstgAgt3Acct /> -->
            <!-- Agent that instructs the next party in the chain to carry out the (set of)
            instruction(s) -->
            <InstgAgt>
                <FinInstnId>
                    <BICFI>BANKAUAAXXX</BICFI>
                </FinInstnId>
            </InstgAgt>
            <!-- Agent that is instructed by the previous party in the chain to carry out the (set
            of) instrction(s) -->
            <InstdAgt>
                <FinInstnId>
                    <BICFI>BANKGBCCXXX</BICFI>
                </FinInstnId>
            </InstdAgt>
            <!-- Agents between the debtor agent and the creditor agent -->
            <!-- <IntrmyAgt1 /> -->
            <!-- <IntrmyAgt1Acct /> -->
            <!-- <IntrmyAgt2 /> -->
            <!-- <IntrmyAgt2Acct /> -->
            <!-- <IntrmyAgt3 /> -->
            <!-- <IntrmyAgt3Acct /> -->
            <!-- Should have no financial regulated direct account relationship with the
            corresponding Debtor. -->
            <UltmtDbtr>
                <Nm>Customer X</Nm> <!-- E.g. individual requesting a Money Transfer Bureau (debtor)
                to execute payment on their behalf to a Creditor -->
            </UltmtDbtr>
            <!-- Initiating Party, usually same as the debtor (thus element not necessary) but could
            also be third party that provides payment initiation services on behalf of the debtor
            (communicating directly with the debtor agent) -->
            <!-- <InitgPty /> -->
            <Dbtr>
                <Nm>Corporation X</Nm>
                <!-- Either structured or unstructured -->
                <PstlAdr>
                    <StrtNm>Main Street</StrtNm>
                    <BldgNb>123</BldgNb>
                    <PstCd>12345</PstCd>
                    <TwnNm>Freiburg</TwnNm>
                    <Ctry>DE</Ctry>
                </PstlAdr>
            </Dbtr>
            <DbtrAcct>
                <Id>
                    <IBAN>**********************</IBAN>
                </Id>
                <Ccy>USD</Ccy>
            </DbtrAcct>
            <DbtrAgt>
                <FinInstnId>
                    <BICFI>BANKAUAAXXX</BICFI>
                </FinInstnId>
            </DbtrAgt>
            <!-- The presence of this element suggests an intermediary financial institution
            (another "Agent") exists in the payment chain, because the Debtor Agent Account element
            would specify which account the Debtor Agent uses at the intermediary institution. -->
            <!-- <DbtrAgtAcct /> -->
            <CdtrAgt>
                <FinInstnId>
                    <BICFI>BANKGBCCXXX</BICFI>
                </FinInstnId>
            </CdtrAgt>
            <!-- The presence of this element suggests an intermediary financial institution
            (another "Agent") exists in the payment chain, because the Creditor Agent Account
            element would specify which account the Creditor Agent uses at the intermediary
            institution. -->
            <!-- <CdtrAgtAcct /> -->
            <Cdtr>
                <Nm>Corporation Y</Nm>
                <PstlAdr>
                    <StrtNm>High Street</StrtNm>
                    <BldgNb>456</BldgNb>
                    <PstCd>67890</PstCd>
                    <TwnNm>London</TwnNm>
                    <Ctry>GB</Ctry>
                </PstlAdr>
            </Cdtr>
            <CdtrAcct>
                <Id>
                    <Othr>
                        <Id>**********************</Id>
                    </Othr>
                </Id>
                <Ccy>USD</Ccy>
            </CdtrAcct>
            <!-- Should have no financial regulated direct account relationship with the
            corresponding Creditor. -->
            <UltmtCdtr>
                <Nm>Customer Y</Nm> <!-- E.g. money is sent to retirement care facility (creditor)
                paying for services the individual (ultimate creditor) is receiving -->
            </UltmtCdtr>
            <!-- Information related to the processing of the payment for these agents -->
            <InstrForCdtrAgt />
            <!-- May not be the creditor agent -->
            <InstrForNxtAgt />
            <!-- Reason for the payment transaction, defined by the debtor, more granular than
            CtgyPurp -->
            <Purp>
                <Cd>IVPT</Cd> <!-- Invoice payment -->
            </Purp>
            <!-- Regulatory Reporting (Regulatory and statutory information needed to report to the
            apropriate authorities)-->
            <RgltryRptg>
                <DbtCdtRptgInd>CRED</DbtCdtRptgInd> <!-- Debit Credit Reporting Indicator (to whom
                does the regulatory reporting information apply?) -->
                <!-- Authority (Name and Country of the Authority/Entity requiring the regulatory
                reporting information) -->
                <Authrty>
                    <Nm>Entity 1</Nm>
                    <Ctry>US</Ctry>
                </Authrty>
                <!-- Details on the regulatory reporting information -->
                <Dtls />
            </RgltryRptg>
            <!-- Related Remittance Information ('remittance' refers to the details explaining what
            a payment is for, helps the beneficiary understand why they received the money and how
            to process or reconcile it, e.g. invoice number)-->
            <RltdRmtInf>
                <RmtId>abc-123</RmtId> <!-- Unique reference assigned by the debtor to identify the
                remittance information sent separately from the payment instruction -->
                <!-- Provides information on either the location or the delivery of remittance
                information -->
                <RmtLctnDtls>
                    <Mtd>EMAL</Mtd> <!-- Method, e.g. Email -->
                    <ElctrncAdr><EMAIL></ElctrncAdr> <!-- Electronic Address, to which an agent
                    is to send the remittance information e.g. the email address (debtor ordering
                    debtor agent to do it) -->
                    <!-- Postal Address to which an agent (debtor agent ordered by the debtor) is to
                    send the remittance information -->
                    <!-- <PstlAdr /> -->
                </RmtLctnDtls>
            </RltdRmtInf>
            <!-- Either structured or unstructured -->
            <RmtInf>
                <Ustrd>Invoice 123</Ustrd>
            </RmtInf>
            <!-- <SplmtryData /> -->
        </CdtTrfTxInf>
    </FIToFICstmrCdtTrf>
</Document>