import {
  CustomRuleWithBaseKeysAndUsageGuidelineRules,
  RequiredDefinition,
} from '../../../../projects/iso20022-lib/rules';

// Rules not encoded:
// R15 - "FIToFICstmrCdtTrf-GrpHdr-SttlmInf" + "-InstgRmbrsmntAgt"/"-InstdRmbrsmntAgt"/"-ThrdRmbrsmntAgt": "Whenever Debtor Agent, Creditor Agent and all agents in between are located within the same country, the clearing code (ClrSysMmbId I guess?) only may be used." -> BICFI only required if not in same country. We can add an info popup for this but cannot enforce it as a rule.
// R19 - "FIToFICstmrCdtTrf-GrpHdr-SttlmInf" + "-InstgRmbrsmntAgt"/"-InstdRmbrsmntAgt"/"-ThrdRmbrsmntAgt": "If the transaction is exchanged on the SWIFT network (i.e. if the sender and receiver of the message are on SWIFT), then BIC is mandatory and other elements are optional, e.g. LEI"

// TODO: There are fields that must adhere to a specific pattern published elsewhere, e.g. "ExternalAccountIdentification1Code". Figure out what to do with these.

// Define what it means to require certain definitions.
// The script parsing the rules will use these to expand the rules. E.g. it is sufficient to specifiy "PstlAddr" in a "require" rule and the script will figure out which definition that corresponds to and expand the rule to include all required subfields of that definition.
// IMPORTANT: This does not denote exclusive ors. An "or" here means that the rule is fulfilled if at least one of the constituents is present.
// IMPORTANT: This denotes the minimal fields that are required to be present. There might be additional rules that add more nuance. E.g. there are numerous rules that describe the relationship of the fields in 'BranchAndFinancialInstitutionIdentification6__1' (see R16, R17, R18, R20, R25, R29), but to express that the 'InstgRmbrsmntAgt' is required, it is sufficient to say, that either the 'BICFI' or the 'Nm' is required. The other rules will then ensure that the other fields are present if one of them is present.
export const sharedRequiredDefinitions: Record<string, RequiredDefinition> = {
  PostalAddress24__1: {
    or: ['AdrLine', ['TwnNm', 'Ctry']], // R21, R26, R30
  },
  BranchAndFinancialInstitutionIdentification6__1: {
    or: ['FinInstnId-BICFI', 'FinInstnId-Nm'],
  },
};

// TODO: Find a way to leave out 'id', 'description', and 'descriptionTranslationProposal'
export const customRules: CustomRuleWithBaseKeysAndUsageGuidelineRules<
  undefined,
  string | undefined
>[] = [
  {
    id: undefined,
    baseKeys: ['FIToFICstmrCdtTrf-GrpHdr'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'serverOnly',
    value: true,
    target: 'MsgId',
  },
  {
    id: undefined,
    baseKeys: ['FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'serverOnly',
    value: true,
    target: 'UETR',
  },
  {
    id: undefined,
    baseKeys: ['FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'serverOnly',
    value: true,
    target: 'ClrSysRef',
  },
  {
    id: undefined,
    baseKeys: ['FIToFICstmrCdtTrf-GrpHdr'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'serverOnly',
    value: true,
    target: 'NbOfTxs',
  },
  {
    id: undefined,
    baseKeys: ['FIToFICstmrCdtTrf-GrpHdr'],
    target: 'NbOfTxs',
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'value',
    value: '1',
    isEqual: true,
  },
  {
    id: undefined,
    baseKeys: ['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId'],
    usageGuidelineRules: ['R12'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'Nm',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        value: true,
        target: 'PstlAdr',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: ['FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId'],
    usageGuidelineRules: ['R12'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'PstlAdr',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        value: true,
        target: 'Nm',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: ['FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt'],
    usageGuidelineRules: ['R13'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'value',
        value: 'CHQB',
        field: 'Cd',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'value', // Not using a pattern here because that would override other patterns for this field which are still valid.
        value: 'HOLD',
        isEqual: false,
        target: 'Cd',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: ['FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt'],
    usageGuidelineRules: ['R14'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'value',
        value: 'PHOB',
        field: 'Cd',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'value', // Not using a pattern here because that would override other patterns for this field which are still valid.
        value: 'TELB',
        isEqual: false,
        target: 'Cd',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: [
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId',
    ],
    usageGuidelineRules: ['R16'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        field: 'BICFI',
        type: 'present',
        value: true,
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'prohibited',
        value: true,
        target: 'ClrSysMmbId',
      },
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'prohibited',
        value: true,
        target: 'Nm',
      },
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'prohibited',
        value: true,
        target: 'PstlAdr',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: [
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId',
    ],
    usageGuidelineRules: ['R17', 'R18'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'ClrSysMmbId',
      },
      {
        type: 'present',
        value: true,
        field: 'Nm',
      },
      {
        type: 'present',
        value: true,
        field: 'PstlAdr',
      },
    ],
    conditionsConnector: 'or',
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'prohibited',
        value: true,
        target: 'BICFI',
      },
    ],
  },
  {
    id: undefined, // This is also covered by R20.
    baseKeys: [
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId',
    ],
    usageGuidelineRules: ['R17', 'R18'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'Nm',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        value: true,
        target: 'PstlAdr',
      },
    ],
  },
  {
    id: undefined, // This is also covered by R20-R25.
    baseKeys: [
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId',
    ],
    usageGuidelineRules: ['R17', 'R18'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'PstlAdr',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        value: true,
        target: 'Nm',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: [
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId',
    ],
    usageGuidelineRules: ['R20', 'R25', 'R29'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'Nm',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        value: true,
        target: 'PstlAdr',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: [
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId',
    ],
    usageGuidelineRules: ['R20', 'R25', 'R29'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'PstlAdr',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        value: true,
        target: 'Nm',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: [
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr',
    ],
    usageGuidelineRules: ['R22', 'R27', 'R31'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'AdrLine',
      },
      {
        conditions: [
          {
            type: 'present',
            value: true,
            field: 'Dept',
          },
          {
            type: 'present',
            value: true,
            field: 'SubDept',
          },
          {
            type: 'present',
            value: true,
            field: 'StrtNm',
          },
          {
            type: 'present',
            value: true,
            field: 'BldgNb',
          },
          {
            type: 'present',
            value: true,
            field: 'BldgNm',
          },
          {
            type: 'present',
            value: true,
            field: 'Flr',
          },
          {
            type: 'present',
            value: true,
            field: 'PstBx',
          },
          {
            type: 'present',
            value: true,
            field: 'Room',
          },
          {
            type: 'present',
            value: true,
            field: 'PstCd',
          },
          {
            type: 'present',
            value: true,
            field: 'TwnLctnNm',
          },
          {
            type: 'present',
            value: true,
            field: 'DstrctNm',
          },
          {
            type: 'present',
            value: true,
            field: 'CtrySubDvsn',
          },
        ],
        conditionsConnector: 'or',
      },
    ],
    conditionsConnector: 'and',
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        value: true,
        target: 'TwnNm',
      },
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        value: true,
        target: 'Ctry',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: [
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr',
    ],
    usageGuidelineRules: ['R22', 'R27', 'R31'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'AdrLine',
      },
      {
        conditions: [
          {
            type: 'present',
            value: true,
            field: 'Dept',
          },
          {
            type: 'present',
            value: true,
            field: 'SubDept',
          },
          {
            type: 'present',
            value: true,
            field: 'StrtNm',
          },
          {
            type: 'present',
            value: true,
            field: 'BldgNb',
          },
          {
            type: 'present',
            value: true,
            field: 'BldgNm',
          },
          {
            type: 'present',
            value: true,
            field: 'Flr',
          },
          {
            type: 'present',
            value: true,
            field: 'PstBx',
          },
          {
            type: 'present',
            value: true,
            field: 'Room',
          },
          {
            type: 'present',
            value: true,
            field: 'PstCd',
          },
          {
            type: 'present',
            value: true,
            field: 'TwnLctnNm',
          },
          {
            type: 'present',
            value: true,
            field: 'DstrctNm',
          },
          {
            type: 'present',
            value: true,
            field: 'CtrySubDvsn',
          },
        ],
        conditionsConnector: 'or',
      },
    ],
    conditionsConnector: 'and',
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'maxItems',
        value: 2,
        target: 'AdrLine',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: [
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr',
    ],
    usageGuidelineRules: ['R23'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'contains',
    target: 'AdrLine',
    value: [
      'Dept',
      'SubDept',
      'StrtNm',
      'BldgNb',
      'BldgNm',
      'Flr',
      'PstBx',
      'Room',
      'PstCd',
      'TwnNm',
      'TwnLctnNm',
      'DstrctNm',
      'CtrySubDvsn',
      'Ctry',
    ],
    contains: false,
  },
  {
    id: undefined,
    baseKeys: [
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr',
    ],
    usageGuidelineRules: ['R24', 'R28', 'R32'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: false,
        field: 'Dept',
      },
      {
        type: 'present',
        value: false,
        field: 'SubDept',
      },
      {
        type: 'present',
        value: false,
        field: 'StrtNm',
      },
      {
        type: 'present',
        value: false,
        field: 'BldgNb',
      },
      {
        type: 'present',
        value: false,
        field: 'BldgNm',
      },
      {
        type: 'present',
        value: false,
        field: 'Flr',
      },
      {
        type: 'present',
        value: false,
        field: 'PstBx',
      },
      {
        type: 'present',
        value: false,
        field: 'Room',
      },
      {
        type: 'present',
        value: false,
        field: 'PstCd',
      },
      {
        type: 'present',
        value: false,
        field: 'TwnNm',
      },
      {
        type: 'present',
        value: false,
        field: 'TwnLctnNm',
      },
      {
        type: 'present',
        value: false,
        field: 'DstrctNm',
      },
      {
        type: 'present',
        value: false,
        field: 'CtrySubDvsn',
      },
      {
        type: 'present',
        value: false,
        field: 'Ctry',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'maxLength',
        value: 35,
        target: 'AdrLine',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: ['FIToFICstmrCdtTrf-GrpHdr-SttlmInf'],
    usageGuidelineRules: ['ThirdReimbursementAgentRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'ThrdRmbrsmntAgt',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        value: true,
        target: 'InstgRmbrsmntAgt',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: ['FIToFICstmrCdtTrf-GrpHdr-SttlmInf'],
    usageGuidelineRules: ['ThirdReimbursementAgentRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'ThrdRmbrsmntAgt',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        value: true,
        target: 'InstdRmbrsmntAgt',
      },
    ],
  },
  {
    id: undefined, // Note: "/Document/FIToFICstmrCdtTrf/GrpHdr/SttlmInf/ClrSys" was removed from the guidelines and is thus not included in this rule.
    baseKeys: ['FIToFICstmrCdtTrf-GrpHdr-SttlmInf'],
    usageGuidelineRules: ['SettlementMethodAgentRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'value',
        value: 'INDA',
        field: 'SttlmMtd',
      },
      {
        type: 'value',
        value: 'INGA',
        field: 'SttlmMtd',
      },
    ],
    conditionsConnector: 'or',
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'prohibited',
        value: true,
        target: 'InstgRmbrsmntAgt',
      },
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'prohibited',
        value: true,
        target: 'InstdRmbrsmntAgt',
      },
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'prohibited',
        value: true,
        target: 'ThrdRmbrsmntAgt',
      },
    ],
  },
  {
    id: undefined, // Note: "/Document/FIToFICstmrCdtTrf/GrpHdr/SttlmInf/ClrSys" was removed from the guidelines and is thus not included in this rule.
    baseKeys: ['FIToFICstmrCdtTrf-GrpHdr-SttlmInf'],
    usageGuidelineRules: ['SettlementMethodCoverRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'value',
        value: 'COVE',
        field: 'SttlmMtd',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'prohibited',
        value: true,
        target: 'SttlmAcct',
      },
    ],
  },
  {
    id: undefined,
    baseKeys: ['FIToFICstmrCdtTrf-GrpHdr-SttlmInf'],
    usageGuidelineRules: ['SettlementMethodCoverAgentRule'],
    description: undefined,
    descriptionTranslationProposal: undefined,
    type: 'condition',
    conditions: [
      {
        type: 'value',
        value: 'COVE',
        field: 'SttlmMtd',
      },
    ],
    rules: [
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        value: true,
        target: 'InstdRmbrsmntAgt',
      },
      {
        id: undefined,
        description: undefined,
        descriptionTranslationProposal: undefined,
        type: 'required',
        value: true,
        target: 'InstgRmbrsmntAgt',
      },
    ],
    rulesConnector: 'or',
  },
  // R33, 34, 35, 36, 37
];
