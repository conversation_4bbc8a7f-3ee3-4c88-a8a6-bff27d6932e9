import { sharedRequiredDefinitions } from '../input/pacs.008/pacs.008.001.08_cbprplus/custom_rules';
import {
  generateRuleId,
  getNestedFieldNames,
  getRef,
  getBasicRuleHash,
  isValidObject,
  isValidStringArray,
  getMergedFieldName,
  generateRuleDescription,
  generateConditionalExplanation,
  concatenateFieldNames,
  moveToFront,
} from './utils';
import {
  Condition,
  Rule,
  NestedCondition,
  isCondition,
  BasicRule,
  RequiredDefinition,
  OrDefinition,
  AndDefinition,
  CustomRuleWithBaseKeysAndUsageGuidelineRules,
  CustomRuleWithUsageGuidelineRules,
  RequiredRule,
  ProhibitedRule,
  CustomConditionalRuleWithUsageGuidelineRules,
} from '../../projects/iso20022-lib/rules';
import { XsdElement } from './types';

function addBaseKeyToRuleAndRemoveBaseKeysAttribute<
  T extends CustomRuleWithUsageGuidelineRules<undefined, string | undefined>
>(rule: T, baseKey: string): T {
  const transformedRule = { ...rule };

  if ('baseKeys' in transformedRule) {
    // Remove the baseKeys property as it is not needed in the transformed rule.
    delete transformedRule.baseKeys;
  }

  if ('target' in transformedRule && transformedRule.target) {
    transformedRule.target = `${baseKey}-${transformedRule.target}`;
  }
  if (
    transformedRule.type === 'contains' &&
    'value' in transformedRule &&
    isValidStringArray(transformedRule.value)
  ) {
    transformedRule.value = transformedRule.value.map(
      (field) => `${baseKey}-${field}`
    );
  }

  // Replace '[base]' in description if the user has provided a description.
  transformedRule.description = transformedRule.description?.replace(
    /\[base\]/g,
    `${baseKey}-`
  );

  return transformedRule;
}

function addBaseKeyToCondition(
  condition: Condition,
  baseKey: string
): Condition {
  const transformed: Condition = { ...condition };

  transformed.field = `${baseKey}-${transformed.field}`;

  if ('otherField' in transformed && transformed.otherField) {
    transformed.otherField = `${baseKey}-${transformed.otherField}`;
  }

  return transformed;
}

function addBaseKeyToNestedCondition(
  condition: NestedCondition,
  baseKey: string
): NestedCondition {
  if (isCondition(condition)) {
    return addBaseKeyToCondition(condition, baseKey);
  } else {
    const transformed: NestedCondition = { ...condition };
    transformed.conditions = transformed.conditions.map((cond: Condition) =>
      addBaseKeyToCondition(cond, baseKey)
    );

    return transformed;
  }
}

/**
 * For each base key, create a new rule with the base key added to all 'id', 'target', 'field', 'otherField', 'otherFields', and 'errorMessage' properties.
 * @param rule the original rule with the 'baseKeys' property
 * @returns an array of new rules with the base keys applied
 */
function getRulesForBaseKeys(
  rule: CustomRuleWithBaseKeysAndUsageGuidelineRules<
    undefined,
    string | undefined
  >
): CustomRuleWithUsageGuidelineRules<undefined, string | undefined>[] {
  if (!rule.baseKeys || rule.baseKeys.length === 0) {
    if (rule.baseKeys) {
      delete rule.baseKeys;
    }
    return [
      rule as CustomRuleWithUsageGuidelineRules<undefined, string | undefined>,
    ];
  }

  return rule.baseKeys.map((baseKey: string) => {
    const transformedRule: CustomRuleWithUsageGuidelineRules<
      undefined,
      string | undefined
    > = addBaseKeyToRuleAndRemoveBaseKeysAttribute<
      CustomRuleWithUsageGuidelineRules<undefined, string | undefined>
    >(rule, baseKey);

    if (
      'conditions' in transformedRule &&
      transformedRule.conditions &&
      transformedRule.conditions.length > 0
    ) {
      transformedRule.conditions = transformedRule.conditions.map(
        (condition: NestedCondition) =>
          addBaseKeyToNestedCondition(condition, baseKey)
      );
    }

    if (
      'rules' in transformedRule &&
      transformedRule.rules &&
      transformedRule.rules.length > 0
    ) {
      transformedRule.rules = transformedRule.rules.map(
        (subRule: BasicRule<undefined, string | undefined>) =>
          addBaseKeyToRuleAndRemoveBaseKeysAttribute<
            BasicRule<undefined, string | undefined>
          >(subRule, baseKey)
      );
    }

    return transformedRule;
  });
}

function addAutogeneratedId(
  rule: CustomRuleWithUsageGuidelineRules<undefined, string | undefined>,
  nameMappings: XsdElement
): CustomRuleWithUsageGuidelineRules<string, string | undefined> {
  const transformedRule: CustomRuleWithUsageGuidelineRules<
    string,
    string | undefined
  > =
    rule.type === 'condition'
      ? { ...rule, id: '', rules: rule.rules.map((r) => ({ ...r, id: '' })) }
      : { ...rule, id: '' };

  // Make sure there are no special characters in the usageGuidelineRules.
  if (
    transformedRule.usageGuidelineRules &&
    !transformedRule.usageGuidelineRules.every((prefix) =>
      /^[a-zA-Z0-9]+$/.test(prefix)
    )
  ) {
    throw new Error(
      `Invalid rule identifier prefixes "${rule.usageGuidelineRules}". Only alphanumeric characters are allowed.`
    );
  }

  const ruleIdPrefix: string = `${rule.usageGuidelineRules?.join('-') ?? ''}`;

  if (transformedRule.type !== 'condition') {
    transformedRule.id = generateRuleId(
      transformedRule.type,
      ruleIdPrefix,
      transformedRule.target
    );
  }

  if (transformedRule.type === 'condition') {
    // Make sure all conditional rules have the same type.
    if (transformedRule.rules.length === 0) {
      throw new Error(
        `Rule with type "condition" must have at least one rule. ${transformedRule}`
      );
    }
    const conditionalRuleType = transformedRule.rules[0].type;
    if (!transformedRule.rules.every((r) => r.type === conditionalRuleType)) {
      throw new Error(
        `All rules in a conditional rule must have the same type. Found types: ${transformedRule.rules
          .map((r) => r.type)
          .join(', ')}. Multiple rule types are not supported as of now.`
      );
    }

    transformedRule.id = generateRuleId(
      'condition',
      ruleIdPrefix,
      getMergedFieldName(
        transformedRule.rules.map((r) => r.target),
        nameMappings
      ),
      {
        conditions: transformedRule.conditions,
        conditionalRuleType,
      }
    );

    const transformedConditionalRules = [];
    for (const conditionalRule of transformedRule.rules) {
      const transformedConditionalRule: BasicRule<string, string | undefined> =
        { ...conditionalRule, id: '' };
      transformedConditionalRule.id = generateRuleId(
        conditionalRule.type,
        ruleIdPrefix,
        conditionalRule.target,
        {
          isConditional: true,
          conditionalParentRuleId: transformedRule.id,
        }
      );
      transformedConditionalRules.push(transformedConditionalRule);
    }

    transformedRule.rules = transformedConditionalRules;
  }

  if (!transformedRule.id) {
    throw new Error(`Rule with type "${rule.type}" does not have an id.`);
  }

  return transformedRule;
}

function getTargetRef(
  target: string,
  definitions: Record<string, unknown>
): string | undefined {
  const targetRefs: string[] = Object.values(definitions).reduce<string[]>(
    (acc, definition) => {
      if (!isValidObject(definition) || !('properties' in definition)) {
        return acc;
      }
      const properties = definition['properties'];
      if (!isValidObject(properties)) {
        return acc;
      }

      for (const propertyKey of Object.keys(properties)) {
        const property = properties[propertyKey];

        if (!isValidObject(property) || !('nestedFieldNames' in property)) {
          continue;
        }

        const nestedFieldNames = property['nestedFieldNames'];

        if (
          isValidStringArray(nestedFieldNames) &&
          nestedFieldNames.includes(target)
        ) {
          const ref = getRef(property);
          if (ref) {
            acc.push(ref);
          }
        }
      }

      return acc;
    },
    []
  );

  if (targetRefs.length === 0) {
    return undefined;
  }
  if (targetRefs.length > 1) {
    throw new Error(
      `Target "${target}" is referenced in multiple definitions: ${targetRefs.join(
        ', '
      )}.`
    );
  }
  return targetRefs[0];
}

function getDefinition(
  ref: string,
  definitions: Record<string, unknown>
): Record<string, unknown> | undefined {
  const definition = definitions[ref];
  if (!isValidObject(definition) || !('type' in definition)) {
    throw new Error(`Definition for ref "${ref}" is not a valid object.`);
  }

  if (definition['type'] !== 'object') {
    return undefined;
  }

  return definition;
}

/**
 * Retrieve a map that contains for each required rule the target reference (e.g. 'PostalAddress24__1').
 */
function getRelevantDefinitionReferences(
  conditionalRequiredRules: RequiredRule<undefined, string | undefined>[],
  definitions: Record<string, unknown>
): Map<string, string> {
  const relevantDefinitions: Map<string, string> = new Map();

  for (const conditionalRequiredRule of conditionalRequiredRules) {
    const target = conditionalRequiredRule.target;
    const targetRef = getTargetRef(target, definitions);
    if (!targetRef) {
      // This means that this rule does not have to be expanded, it has no ref and thus there is no further nesting.
      continue;
    }

    const nestedDefinition = getDefinition(targetRef, definitions);
    if (!nestedDefinition) {
      // This means that this rule does not have to be expanded, the rule is not of type "object" and thus there is no further nesting.
      continue;
    }

    if (!(targetRef in sharedRequiredDefinitions)) {
      throw new Error(
        `Target "${target}" is not defined in sharedRequiredDefinitions.`
      );
    }

    // Just to make sure there are no hash collisions
    if (relevantDefinitions.has(getBasicRuleHash(conditionalRequiredRule))) {
      throw new Error(
        `Target "${target}" is already defined in relevantDefinitions. This should not happen.`
      );
    }

    relevantDefinitions.set(
      getBasicRuleHash(conditionalRequiredRule),
      targetRef
    );
  }

  return relevantDefinitions;
}

function connectAnd(and: AndDefinition): string {
  if (and.length === 0) {
    return '';
  }
  if (and.length === 1) {
    return and[0];
  }
  if (and.length === 2) {
    return `(${and[0]} and ${and[1]})`;
  }
  return `(${and.join(', and ')})`;
}

function getExpandedRequiredRule(
  conditionalRequiredRule: RequiredRule<undefined, string | undefined>,
  expansion: string,
  requiredDefinition: RequiredDefinition
): RequiredRule<undefined, string | undefined> {
  // Split the requiredDefinition into the entry including the expansion and the other entries such that we can build the description from it.
  let expansionEntry: string = expansion;
  let otherEntries: string[] = [];
  if ('and' in requiredDefinition) {
    otherEntries =
      requiredDefinition.and?.filter((entry: string) => entry !== expansion) ||
      [];
  } else if ('or' in requiredDefinition) {
    const nonNestedOrEntries: string[] =
      requiredDefinition.or?.filter(
        (entry: string | AndDefinition) => typeof entry === 'string'
      ) || [];
    const nonNestedOrEntriesWithoutExpansion: string[] =
      nonNestedOrEntries.filter((entry: string) => entry !== expansion);
    const nestedOrEntries: AndDefinition[] =
      requiredDefinition.or?.filter((entry: string | AndDefinition) =>
        isValidStringArray(entry)
      ) || [];
    const nestedOrEntriesWithoutExpansion: AndDefinition[] =
      nestedOrEntries.filter(
        (entry: AndDefinition) => !entry.includes(expansion)
      );
    const nestedExpansion: AndDefinition | undefined =
      nestedOrEntries.length !== nestedOrEntriesWithoutExpansion.length
        ? nestedOrEntries.find((entry: AndDefinition) =>
            entry.includes(expansion)
          )
        : undefined;
    expansionEntry = nestedExpansion ? connectAnd(nestedExpansion) : expansion;

    otherEntries = [
      ...nonNestedOrEntriesWithoutExpansion,
      ...nestedOrEntriesWithoutExpansion.map((entry: AndDefinition) =>
        connectAnd(entry)
      ),
    ];
  }

  let description = conditionalRequiredRule.description;

  if ('or' in requiredDefinition) {
    if (otherEntries.length === 0) {
      description += ` For this, ${expansionEntry} must be present.`;
    }
    if (otherEntries.length === 1) {
      description += ` For this, either ${expansionEntry} or ${otherEntries[0]} must be present.`;
    }
    if (otherEntries.length > 1) {
      description += ` For this, either ${expansionEntry}, or ${otherEntries.join(
        ', or '
      )} must be present.`;
    }
  } else {
    if (otherEntries.length === 0) {
      description += ` For this, ${expansionEntry} must be present.`;
    }
    if (otherEntries.length === 1) {
      description += ` For this, ${expansionEntry} and ${otherEntries[0]} must be present.`;
    }
    if (otherEntries.length > 1) {
      description += ` For this, ${expansionEntry}, and ${otherEntries.join(
        ', and '
      )} must be present.`;
    }
  }

  return {
    ...conditionalRequiredRule,
    description,
    target: `${conditionalRequiredRule.target}-${expansion}`,
  };
}

function getRequiredRulesForAnd(
  and: AndDefinition,
  conditionalRequiredRule: RequiredRule<undefined, string | undefined>
): RequiredRule<undefined, string | undefined>[] {
  return and.map((andEntry: string) => {
    return getExpandedRequiredRule(conditionalRequiredRule, andEntry, { and });
  });
}

function getRequiredRulesForOr(
  or: OrDefinition,
  conditionalRequiredRule: RequiredRule<undefined, string | undefined>
): RequiredRule<undefined, string | undefined>[] {
  return or.map((orEntry: string | string[]) => {
    // If the entry is an array (denoting an "and" condition), only take the first value. The remaining values will be handled in a separate conditional rule.
    const orKey = Array.isArray(orEntry) ? orEntry[0] : orEntry;

    return getExpandedRequiredRule(conditionalRequiredRule, orKey, { or });
  });
}

/**
 * Go through the "or" array and check if there are any entries that are arrays (denoting an "and" condition).
 * E.g. "or: ['field1', ['field2', 'field3']]" - this means that either 'field1' must be present or both 'field2' and 'field3' must be present.
 * Only the first value from the "and" array was taken. Now define a further conditional rule that requires all other fields from the "and" array as soon as the first value is present.
 */
function getAdditionalConditionalRules(
  or: (string | AndDefinition)[],
  originalConditionalRequiredRule: RequiredRule<undefined, string | undefined>,
  originalUsageGuidelineRules: string[] | undefined
): CustomRuleWithUsageGuidelineRules<undefined, string | undefined>[] {
  const additionalRules: CustomRuleWithUsageGuidelineRules<
    undefined,
    string | undefined
  >[] = [];

  for (const orEntry of or) {
    if (!Array.isArray(orEntry) || orEntry.length <= 1) {
      // If the entry is not an array or has only one entry, we don't need to create a conditional rule.
      continue;
    }

    const additionalRequiredFields = orEntry.slice(1);
    const presentConditionField = `${originalConditionalRequiredRule.target}-${orEntry[0]}`;

    const newConditionalRule: CustomRuleWithUsageGuidelineRules<
      undefined,
      string | undefined
    > = {
      usageGuidelineRules: originalUsageGuidelineRules,
      id: undefined,
      description: `If ${presentConditionField} is present, then ${connectAnd(
        additionalRequiredFields.map(
          (field) => `${originalConditionalRequiredRule.target}-${field}`
        )
      )} must also be present.`,
      descriptionTranslationProposal: undefined,
      type: 'condition',
      conditions: [
        {
          type: 'present',
          value: true,
          field: presentConditionField,
        },
      ],
      rules: additionalRequiredFields.map((field) => ({
        id: undefined,
        description: `If ${presentConditionField} is present, then ${originalConditionalRequiredRule.target}-${field} must also be present.`,
        descriptionTranslationProposal: undefined,
        type: 'required',
        value: true,
        target: `${originalConditionalRequiredRule.target}-${field}`,
      })),
      rulesConnector: 'and',
    };
    additionalRules.push(newConditionalRule);
  }

  return additionalRules;
}

/**
 * Expands a rule with type "condition" that has a "required" target into multiple rules based on the sharedRequiredDefinitions.
 * @param rule the rule to expand
 * @param jsonSchema the JSON schema to use for the expansion
 * @returns an array of rules that have been expanded from the original rule
 */
function expandRequiredTargets(
  rule: CustomRuleWithUsageGuidelineRules<undefined, string | undefined>,
  definitions: Record<string, unknown>
): CustomRuleWithUsageGuidelineRules<undefined, string | undefined>[] {
  if (rule.type !== 'condition') {
    return [rule];
  }

  const conditionalRules = rule.rules;
  const conditionalRequiredRules = conditionalRules.filter(
    (rule: BasicRule<undefined, string | undefined>) => rule.type === 'required'
  );

  const relevantDefinitionRefPerRule = getRelevantDefinitionReferences(
    conditionalRequiredRules,
    definitions
  );

  if (relevantDefinitionRefPerRule.size === 0) {
    return [rule];
  }

  const relevantDefinitionRefs = conditionalRequiredRules.reduce<string[]>(
    (acc: string[], rule: RequiredRule<undefined, string | undefined>) => {
      const targetRef = relevantDefinitionRefPerRule.get(
        getBasicRuleHash(rule)
      );
      if (!targetRef) {
        return acc;
      }
      acc.push(targetRef);
      return acc;
    },
    []
  );
  const requiredDefinitions = relevantDefinitionRefs.map(
    (targetRef: string) => sharedRequiredDefinitions[targetRef]
  );

  if (
    !requiredDefinitions.every(
      (definition: RequiredDefinition) =>
        'and' in definition || 'or' in definition
    )
  ) {
    throw new Error(
      `No "and" or "or" entries found in a shared RequiredDefinition.`
    );
  }

  const expandedRules: CustomRuleWithUsageGuidelineRules<
    undefined,
    string | undefined
  >[] = [];

  const unaffectedConditionalRules = conditionalRules.filter(
    (r: BasicRule<undefined, string | undefined>) =>
      !relevantDefinitionRefPerRule.has(getBasicRuleHash(r))
  );
  let didExtractUnaffectedConditionalRules = false;

  if (
    rule.rulesConnector === 'or' &&
    requiredDefinitions.some(
      (definition: RequiredDefinition) => 'and' in definition
    )
  ) {
    throw new Error(
      `Rule ${rule} has a rulesConnector of "or" and there is there is a required definition with type "and" to be expanded. This is not supported as of now.`
    );
  } else if (
    (typeof rule.rulesConnector === 'undefined' ||
      rule.rulesConnector === 'and') &&
    conditionalRules.length > 1
  ) {
    // If it's just one conditional rule, the rulesConnector does not matter. If there is more than one conditional rule and all required definitions are of type "or" or all required definitions are of type "and", this is still not an issue. For the "or" case we just move out the original rules into their own rule.
    if (
      requiredDefinitions.some(
        (definition: RequiredDefinition) => 'or' in definition
      ) &&
      requiredDefinitions.some(
        (definition: RequiredDefinition) => 'and' in definition
      )
    ) {
      throw new Error(
        `Rule ${rule.id} has a rulesConnector of "and" and there is there is a mixture of required definitions with type "or" and "and" to be expanded. This is not supported as of now.`
      );
    } else if (
      requiredDefinitions.every(
        (definition: RequiredDefinition) => 'or' in definition
      )
    ) {
      // Move out the remaining rules to a new conditional rule with 'rulesConnector: "and"'
      const newConditionalRule: CustomRuleWithUsageGuidelineRules<
        undefined,
        string | undefined
      > = {
        ...rule,
        rules: unaffectedConditionalRules,
        rulesConnector: 'and',
      };
      expandedRules.push(newConditionalRule);
      didExtractUnaffectedConditionalRules = true;
    }
  }

  const expandedConditionalRequiredRules: RequiredRule<
    undefined,
    string | undefined
  >[] = [];

  for (const ruleHash of relevantDefinitionRefPerRule.keys()) {
    const conditionalRequiredRule = conditionalRequiredRules.find(
      (r: RequiredRule<undefined, string | undefined>) =>
        getBasicRuleHash(r) === ruleHash
    );
    if (!conditionalRequiredRule) {
      throw new Error(`No "required" rule found for rule ${rule}.`);
    }

    const targetRef = relevantDefinitionRefPerRule.get(ruleHash);
    if (!targetRef) {
      throw new Error(`No targetRef found for rule ${rule.id}.`);
    }

    const requiredDefinition = sharedRequiredDefinitions[targetRef];

    if ('and' in requiredDefinition && requiredDefinition.and) {
      // Generate required rules for all entries in the "and" array.
      const and = requiredDefinition.and;
      const newConditionalRequiredRules: RequiredRule<
        undefined,
        string | undefined
      >[] = getRequiredRulesForAnd(and, conditionalRequiredRule);
      expandedConditionalRequiredRules.push(...newConditionalRequiredRules);
    } else if ('or' in requiredDefinition && requiredDefinition.or) {
      const or = requiredDefinition.or;
      // Generate required rules for all entries in the "or" array.
      // If there is an array in the "or" array (e.g. ['field1', ['field2', 'field3']]), we only take the first value and create additional conditional rules that require the remaining values (see below).
      const newConditionalRequiredRules: RequiredRule<
        undefined,
        string | undefined
      >[] = getRequiredRulesForOr(or, conditionalRequiredRule);
      expandedConditionalRequiredRules.push(...newConditionalRequiredRules);
      // Create additional conditional rules that require the remaining values.
      const additionalConditionalRules = getAdditionalConditionalRules(
        or,
        conditionalRequiredRule,
        rule.usageGuidelineRules
      );
      expandedRules.push(...additionalConditionalRules);
    }
  }

  const transformedRule = { ...rule };
  transformedRule.rules = didExtractUnaffectedConditionalRules
    ? [...expandedConditionalRequiredRules]
    : [...unaffectedConditionalRules, ...expandedConditionalRequiredRules];
  transformedRule.rulesConnector = requiredDefinitions.some(
    (definition: RequiredDefinition) => 'and' in definition
  )
    ? 'and'
    : 'or';
  expandedRules.push(transformedRule);

  return expandedRules;
}

/**
 * Expands a rule with type "condition" that has a "prohibited" target into multiple rules.
 * @param rule the rule to expand
 * @param jsonSchema the JSON schema to use for the expansion
 * @returns an array of rules that have been expanded from the original rule
 */
function expandProhibitedTargets(
  rule: CustomRuleWithUsageGuidelineRules<undefined, string | undefined>,
  definitions: Record<string, unknown>
): CustomRuleWithUsageGuidelineRules<undefined, string | undefined>[] {
  if (rule.type !== 'condition') {
    return [rule];
  }

  const conditionalRules = rule.rules;
  const conditionalProhibitedRules = conditionalRules.filter(
    (rule: BasicRule<undefined, string | undefined>) =>
      rule.type === 'prohibited'
  );
  const conditionalNonProhibitedRules = conditionalRules.filter(
    (rule: BasicRule<undefined, string | undefined>) =>
      rule.type !== 'prohibited'
  );

  // We only allow 'rulesConnector: "and"' for conditional rules with "prohibited" rules.
  if (rule.rulesConnector === 'or' && conditionalProhibitedRules.length > 0) {
    throw new Error(
      `Rule ${rule.id} has a rulesConnector of "or" and there is a prohibited rule. This is not supported as of now.`
    );
  }

  const relevantDefinitions: Map<string, string> = new Map();

  for (const conditionalProhibitedRule of conditionalProhibitedRules) {
    const target = conditionalProhibitedRule.target;
    const targetRef = getTargetRef(target, definitions);
    if (!targetRef) {
      continue;
    }

    const nestedDefinition = getDefinition(targetRef, definitions);
    if (!nestedDefinition) {
      continue;
    }

    relevantDefinitions.set(target, targetRef);
  }

  if (relevantDefinitions.size === 0) {
    return [rule];
  }

  const newConditionalProhibitedRules: ProhibitedRule<
    undefined,
    string | undefined
  >[] = [];

  for (const [target, targetRef] of relevantDefinitions.entries()) {
    const prohibitedRuleToExpand = conditionalProhibitedRules.find(
      (rule: ProhibitedRule<undefined, string | undefined>) =>
        rule.target === target
    );

    if (!prohibitedRuleToExpand) {
      throw new Error(
        `No "prohibited" rule found for target "${target}" in rule ${rule.id}.`
      );
    }

    const allNestedFieldNames = getNestedFieldNames(
      definitions,
      targetRef,
      target
    );

    newConditionalProhibitedRules.push(
      ...allNestedFieldNames.map(
        (fieldName): ProhibitedRule<undefined, string | undefined> => ({
          ...prohibitedRuleToExpand,
          id: undefined,
          // id: addBaseKey(
          //   prohibitedRuleToExpand.id,
          //   fieldName,
          //   '',
          //   '-prohibited-conditional',
          //   true
          // ),
          description: `${prohibitedRuleToExpand.description} This means, that ${fieldName} must not be present.`,
          target: fieldName,
        })
      )
    );
  }

  const conditionalProhibitedRulesNotToExpand =
    conditionalProhibitedRules.filter(
      (rule: ProhibitedRule<undefined, string | undefined>) =>
        !relevantDefinitions.has(rule.target)
    );

  // Spread out the "prohibited" rules into multiple rules.
  return [
    {
      ...rule,
      rules: [
        ...conditionalNonProhibitedRules,
        ...conditionalProhibitedRulesNotToExpand,
        ...newConditionalProhibitedRules,
      ],
      rulesConnector: 'and',
    },
  ];
}

/**
 * Expand conditions of type "present" to include all subfields. If there is a sibling to the condition connected with "and", extract that into its own rule.
 * @param rule
 * @param definitions
 * @returns
 */
function expandPresentConditions(
  rule: CustomRuleWithUsageGuidelineRules<undefined, string | undefined>,
  definitions: Record<string, unknown>
): CustomRuleWithUsageGuidelineRules<undefined, string | undefined>[] {
  if (rule.type !== 'condition') {
    return [rule];
  }

  const conditions: NestedCondition[] = rule.conditions;

  const relevantDefinitions: Map<string, string> = new Map();

  for (const condition of conditions) {
    if (isCondition(condition)) {
      if (condition.type !== 'present' || condition.value !== true) {
        continue;
      }
      const target = condition.field;
      const targetRef = getTargetRef(target, definitions);
      if (!targetRef) {
        continue;
      }
      const nestedDefinition = getDefinition(targetRef, definitions);
      if (!nestedDefinition) {
        continue;
      }
      relevantDefinitions.set(target, targetRef);
    } else {
      // Nested conditions cannot be expanded as of now.
      for (const subCondition of condition.conditions) {
        if (subCondition.type !== 'present' || subCondition.value !== true) {
          continue;
        }
        const target = subCondition.field;
        const targetRef = getTargetRef(target, definitions);
        if (!targetRef) {
          continue;
        }
        const nestedDefinition = getDefinition(targetRef, definitions);
        if (nestedDefinition) {
          throw new Error(
            `Found a nested condition in rule ${rule.id} that requires expansion. Expansion is not supported for nested conditions as of now.`
          );
        }
      }
    }
  }

  if (relevantDefinitions.size === 0) {
    return [rule];
  }

  if (
    (typeof rule.conditionsConnector === 'undefined' ||
      rule.conditionsConnector === 'and') &&
    relevantDefinitions.size > 1
  ) {
    throw new Error(
      `Multiple conditions connected with "and" found in rule ${rule.id} that need to be expanded. This is not supported as of now.`
    );
  }

  const expandedConditions: NestedCondition[] = [];

  for (const [field, targetRef] of relevantDefinitions.entries()) {
    const presentConditionToExpand = conditions.find(
      (condition) => isCondition(condition) && condition.field === field
    );

    if (!presentConditionToExpand || !isCondition(presentConditionToExpand)) {
      throw new Error(
        `No "present" condition found for field "${field}" in rule ${rule.id}.`
      );
    }

    const allNestedFieldNames = getNestedFieldNames(
      definitions,
      targetRef,
      field
    );

    expandedConditions.push(
      ...allNestedFieldNames.map(
        (fieldName): NestedCondition => ({
          type: 'present',
          value: true,
          field: fieldName,
        })
      )
    );
  }

  const conditionsNotToExpand = conditions.filter(
    (condition) =>
      !isCondition(condition) || !relevantDefinitions.has(condition.field)
  );

  if (rule.conditionsConnector === 'or' || conditions.length === 1) {
    // Simple case: We just spread out the "present" condition into multiple conditions.
    return [
      {
        ...rule,
        conditions: [...expandedConditions, ...conditionsNotToExpand],
        conditionsConnector: 'or',
      },
    ];
  } else {
    // There are multiple conditions connected with "and". Create a new rule containing the conditions not to expand.
    const newConditionalRule: CustomConditionalRuleWithUsageGuidelineRules<
      undefined,
      string | undefined
    > = {
      ...rule,
      conditions: conditionsNotToExpand,
      conditionsConnector: 'and',
    };

    return [
      newConditionalRule,
      {
        ...rule,
        conditions: expandedConditions,
        conditionsConnector: 'or',
      },
    ];
  }
}

function addAutogeneratedDescription(
  rule: CustomRuleWithUsageGuidelineRules<string, string | undefined>,
  nameMappings: XsdElement
): CustomRuleWithUsageGuidelineRules<string, string> {
  const transformedRule: CustomRuleWithUsageGuidelineRules<string, string> =
    rule.type === 'condition'
      ? {
          ...rule,
          description: rule.description || '',
          descriptionTranslationProposal:
            rule.descriptionTranslationProposal || '',
          rules: rule.rules.map((r) => ({
            ...r,
            description: r.description || '',
            descriptionTranslationProposal:
              r.descriptionTranslationProposal || '',
          })),
        }
      : {
          ...rule,
          description: rule.description || '',
          descriptionTranslationProposal:
            rule.descriptionTranslationProposal || '',
        };

  const ruleType = transformedRule.type;

  if (ruleType !== 'condition') {
    if (transformedRule.description) {
      console.log(
        `Rule with type "${ruleType}" already has a description. Using the existing description.`
      );
    } else {
      transformedRule.description = generateRuleDescription(
        ruleType,
        transformedRule.target,
        nameMappings,
        'en',
        {
          ruleDependentValue:
            ruleType === 'required' ||
            ruleType === 'prohibited' ||
            ruleType === 'maxLength' ||
            ruleType === 'pattern' ||
            ruleType === 'maxItems' ||
            ruleType === 'contains' ||
            ruleType === 'serverOnly' ||
            ruleType === 'value'
              ? transformedRule.value
              : undefined,
          secondRuleDependentValue:
            ruleType === 'value'
              ? transformedRule.isEqual
              : ruleType === 'contains'
              ? transformedRule.contains
              : undefined,
        }
      );
    }
    if (transformedRule.descriptionTranslationProposal) {
      console.log(
        `Rule with type "${ruleType}" already has a description translation proposal. Using the existing description translation proposal.`
      );
    } else {
      transformedRule.descriptionTranslationProposal = generateRuleDescription(
        ruleType,
        transformedRule.target,
        nameMappings,
        'de',
        {
          ruleDependentValue:
            ruleType === 'required' ||
            ruleType === 'prohibited' ||
            ruleType === 'maxLength' ||
            ruleType === 'pattern' ||
            ruleType === 'maxItems' ||
            ruleType === 'contains' ||
            ruleType === 'serverOnly' ||
            ruleType === 'value'
              ? transformedRule.value
              : undefined,
          secondRuleDependentValue:
            ruleType === 'value'
              ? transformedRule.isEqual
              : ruleType === 'contains'
              ? transformedRule.contains
              : undefined,
        }
      );
    }
  }

  if (transformedRule.type === 'condition') {
    if (
      transformedRule.description &&
      transformedRule.rules.every((r) => r.description)
    ) {
      console.log(
        `Rule with type "condition" already has a description including all nested rules. Using the existing description.`
      );
    } else {
      if (transformedRule.rules.length === 0) {
        throw new Error(
          `Rule with type "condition" must have at least one rule. ${transformedRule}`
        );
      }

      transformedRule.description = generateRuleDescription(
        'condition',
        concatenateFieldNames(
          transformedRule.rules.map((r) => r.target),
          transformedRule.rulesConnector ?? 'and',
          'en',
          nameMappings
        ),
        nameMappings,
        'en',
        {
          conditions: transformedRule.conditions,
          conditionsConnector: transformedRule.conditionsConnector ?? 'and',
          conditionalRules: transformedRule.rules,
          conditionalRulesConnector: transformedRule.rulesConnector ?? 'and',
        }
      );

      const transformedConditionalRules = [];
      for (const conditionalRule of transformedRule.rules) {
        const transformedConditionalRule: BasicRule<string, string> = {
          ...conditionalRule,
        };
        const conditionalRuleType = transformedConditionalRule.type;
        transformedConditionalRule.description = generateRuleDescription(
          conditionalRule.type,
          concatenateFieldNames(
            moveToFront(
              transformedRule.rules.map((r) => r.target),
              conditionalRule.target
            ),
            transformedRule.rulesConnector ?? 'and',
            'en',
            nameMappings
          ),
          nameMappings,
          'en',
          {
            ruleDependentValue:
              conditionalRuleType === 'required' ||
              conditionalRuleType === 'prohibited' ||
              conditionalRuleType === 'maxLength' ||
              conditionalRuleType === 'pattern' ||
              conditionalRuleType === 'maxItems' ||
              conditionalRuleType === 'value'
                ? transformedConditionalRule.value
                : undefined,
            secondRuleDependentValue:
              conditionalRuleType === 'value'
                ? transformedConditionalRule.isEqual
                : undefined,
            conditionalExplanation: generateConditionalExplanation(
              transformedRule.conditions,
              transformedRule.conditionsConnector ?? 'and',
              'en',
              nameMappings
            ),
          }
        );
        transformedConditionalRules.push(transformedConditionalRule);
      }

      transformedRule.rules = transformedConditionalRules;
    }
    if (
      transformedRule.descriptionTranslationProposal &&
      transformedRule.rules.every((r) => r.descriptionTranslationProposal)
    ) {
      console.log(
        `Rule with type "condition" already has a description translation proposal including all nested rules. Using the existing description.`
      );
    } else {
      if (transformedRule.rules.length === 0) {
        throw new Error(
          `Rule with type "condition" must have at least one rule. ${transformedRule}`
        );
      }

      transformedRule.descriptionTranslationProposal = generateRuleDescription(
        'condition',
        concatenateFieldNames(
          transformedRule.rules.map((r) => r.target),
          transformedRule.rulesConnector ?? 'and',
          'de',
          nameMappings
        ),
        nameMappings,
        'de',
        {
          conditions: transformedRule.conditions,
          conditionsConnector: transformedRule.conditionsConnector ?? 'and',
          conditionalRules: transformedRule.rules,
          conditionalRulesConnector: transformedRule.rulesConnector ?? 'and',
        }
      );

      const transformedConditionalRules = [];
      for (const conditionalRule of transformedRule.rules) {
        const transformedConditionalRule: BasicRule<string, string> = {
          ...conditionalRule,
        };
        const conditionalRuleType = transformedConditionalRule.type;
        transformedConditionalRule.descriptionTranslationProposal =
          generateRuleDescription(
            conditionalRule.type,
            concatenateFieldNames(
              moveToFront(
                transformedRule.rules.map((r) => r.target),
                conditionalRule.target
              ),
              transformedRule.rulesConnector ?? 'and',
              'de',
              nameMappings
            ),
            nameMappings,
            'de',
            {
              ruleDependentValue:
                conditionalRuleType === 'required' ||
                conditionalRuleType === 'prohibited' ||
                conditionalRuleType === 'maxLength' ||
                conditionalRuleType === 'pattern' ||
                conditionalRuleType === 'maxItems' ||
                conditionalRuleType === 'value'
                  ? transformedConditionalRule.value
                  : undefined,
              secondRuleDependentValue:
                conditionalRuleType === 'value'
                  ? transformedConditionalRule.isEqual
                  : undefined,
              conditionalExplanation: generateConditionalExplanation(
                transformedRule.conditions,
                transformedRule.conditionsConnector ?? 'and',
                'de',
                nameMappings
              ),
            }
          );
        transformedConditionalRules.push(transformedConditionalRule);
      }

      transformedRule.rules = transformedConditionalRules;
    }
  }

  if (
    !(
      transformedRule.description &&
      transformedRule.descriptionTranslationProposal
    )
  ) {
    throw new Error(
      `Rule with type "${rule.type}" does not have a description and description translation proposal.`
    );
  }

  return transformedRule;
}

function addUsageGuidelineRulesToDescriptions(
  rule: CustomRuleWithUsageGuidelineRules<string, string>
): Rule {
  const transformedRule = { ...rule };
  const descriptionSuffix = rule.usageGuidelineRules?.join(', ');
  const description = transformedRule.description;
  const descriptionTranslationProposal =
    transformedRule.descriptionTranslationProposal;

  transformedRule.description = `${description}${
    descriptionSuffix ? ` (${descriptionSuffix})` : ''
  }`;
  transformedRule.descriptionTranslationProposal = `${descriptionTranslationProposal}${
    descriptionSuffix ? ` (${descriptionSuffix})` : ''
  }`;

  if (transformedRule.type === 'condition') {
    const transformedConditionalRules: BasicRule[] = [];
    for (const conditionalRule of transformedRule.rules) {
      const transformedConditionalRule = { ...conditionalRule };
      transformedConditionalRule.description = `${conditionalRule.description}${
        descriptionSuffix ? ` (${descriptionSuffix})` : ''
      }`;
      transformedConditionalRule.descriptionTranslationProposal = `${
        conditionalRule.descriptionTranslationProposal
      }${descriptionSuffix ? ` (${descriptionSuffix})` : ''}`;
      transformedConditionalRules.push(transformedConditionalRule);
    }
    transformedRule.rules = transformedConditionalRules;
  }

  if (transformedRule.usageGuidelineRules) {
    delete transformedRule.usageGuidelineRules;
  }

  return transformedRule;
}

export function transformRules(
  rules: CustomRuleWithBaseKeysAndUsageGuidelineRules<
    undefined,
    string | undefined
  >[],
  jsonSchema: Record<string, unknown>,
  nameMappings: XsdElement
): Rule[] {
  if (!isValidObject(jsonSchema) || !('definitions' in jsonSchema)) {
    throw new Error('Invalid JSON schema format.');
  }

  const { definitions } = jsonSchema;

  if (!isValidObject(definitions)) {
    throw new Error('Invalid definitions in JSON schema.');
  }

  // Add the base keys to the rule (creating new rules if there are multiple base keys) and remove the 'baseKeys' property.
  // Allows the switch from 'CustomRuleWithBaseKeysAndUsageGuidelineRules' to 'CustomRuleWithUsageGuidelineRules'
  const allOriginalRules = rules.reduce<
    CustomRuleWithUsageGuidelineRules<undefined, string | undefined>[]
  >((acc, rule) => {
    acc.push(...getRulesForBaseKeys(rule));
    return acc;
  }, []);

  const rulesWithExpandedRequiredTargets = allOriginalRules.reduce<
    CustomRuleWithUsageGuidelineRules<undefined, string | undefined>[]
  >((acc, rule) => {
    const expandedRules = expandRequiredTargets(rule, definitions);
    acc.push(...expandedRules);
    return acc;
  }, []);

  const rulesWithExpandedProhibitedTargets =
    rulesWithExpandedRequiredTargets.reduce<
      CustomRuleWithUsageGuidelineRules<undefined, string | undefined>[]
    >((acc, rule) => {
      const expandedRules = expandProhibitedTargets(rule, definitions);
      acc.push(...expandedRules);
      return acc;
    }, []);

  const rulesWithExpandedPresentConditions =
    rulesWithExpandedProhibitedTargets.reduce<
      CustomRuleWithUsageGuidelineRules<undefined, string | undefined>[]
    >((acc, rule) => {
      const expandedRules = expandPresentConditions(rule, definitions);
      acc.push(...expandedRules);
      return acc;
    }, []);

  // Add IDs to all rules, allowing the switch from 'CustomRuleWithUsageGuidelineRules<string | undefined, string | undefined>' to 'CustomRuleWithUsageGuidelineRules<string, string | undefined>'.
  const rulesWithIds = rulesWithExpandedPresentConditions.map(
    (
      rule: CustomRuleWithUsageGuidelineRules<undefined, string | undefined>
    ): CustomRuleWithUsageGuidelineRules<string, string | undefined> =>
      addAutogeneratedId(rule, nameMappings)
  );

  // Add descriptions to all rules where the user did not provide any allowing the switch from 'CustomRuleWithUsageGuidelineRules<string, string | undefined>' to 'CustomRuleWithUsageGuidelineRules<string, string>'.
  const rulesWithDescriptions: CustomRuleWithUsageGuidelineRules<
    string,
    string
  >[] = rulesWithIds.map(
    (rule: CustomRuleWithUsageGuidelineRules<string, string | undefined>) =>
      addAutogeneratedDescription(rule, nameMappings)
  );

  const rulesWithUsageGuidelineRulesAddedToDescriptions =
    rulesWithDescriptions.map((rule) =>
      addUsageGuidelineRulesToDescriptions(rule)
    );

  return rulesWithUsageGuidelineRulesAddedToDescriptions;
}
