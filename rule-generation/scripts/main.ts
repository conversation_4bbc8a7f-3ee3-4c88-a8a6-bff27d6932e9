#!/usr/bin/env node

import * as fs from 'fs';
import { Document, DOMParser } from '@xmldom/xmldom';
import minimist from 'minimist';
import { XsdElement } from './types';
import { extractXsdElementMappings } from './extract-xsd-names';
import { toSnakeCase } from './utils';
import {
  getAllFieldsFromJSONSchema,
  transformJsonSchema,
} from './transform-json-schema';
import { generateRules } from './generate-rules';
import { transformRules } from './transform-rules';
import { customRules } from '../input/pacs.008/pacs.008.001.08_cbprplus/custom_rules';
import { combineRules } from './combine-rules';
import { validateRules } from './validate-rules';
import { filterClientRules } from './filter-client-rules';
import {
  computeAffectedFieldsForFieldValueChange,
  mapToObject,
} from './precompute-utilities';
import {
  expandFormSetup,
  verifyContainsAllFieldsFromSchema,
} from './expand-form-setup';
import { findCrossPageDependencies } from './find-cross-page-dependencies';
import { replaceExtensionNames } from './replace-extensions';

const INTERMEDIARY_RESULTS_FOLDER =
  'rule-generation/intermediary-results/pacs_008_001_08';

/**
 * Parse command line arguments
 */
function parseArgs(): {
  jsonFile: string;
  xsdFile: string;
  formSetupFile: string;
  outputFolder: string;
  baseElement?: string;
} {
  const argv = minimist(process.argv.slice(2), {
    string: ['output-folder', 'base-element'],
    default: {
      outputFolder: '',
    },
  });

  if (argv._.length < 2) {
    console.error(
      'Usage: node transform-json-schema.js <json_schema> <xsd_schema> <form_setup> [--output-folder /output] [--base-element element_name]'
    );
    process.exit(1);
  }

  return {
    jsonFile: argv._[0],
    xsdFile: argv._[1],
    formSetupFile: argv._[2],
    outputFolder: argv['output-folder'],
    baseElement: argv['base-element'],
  };
}

function writeFile(content: string | object, filePath: string): void {
  fs.writeFileSync(filePath, JSON.stringify(content, null, 2), 'utf8');
  console.log(`Wrote to ${filePath}`);
}

async function main() {
  try {
    const args = parseArgs();

    // Read input files
    const jsonSchemaContent = fs.readFileSync(args.jsonFile, 'utf8');
    const xsdContent = fs.readFileSync(args.xsdFile, 'utf8');
    const formSetupContent = fs.readFileSync(args.formSetupFile, 'utf8');

    // Parse input files
    const jsonSchema = JSON.parse(jsonSchemaContent);
    const formSetup = JSON.parse(formSetupContent);
    const parser = new DOMParser();
    const xmlDoc: Document = parser.parseFromString(xsdContent, 'text/xml');

    const nameMappings: XsdElement = extractXsdElementMappings(xmlDoc);
    writeFile(
      nameMappings,
      `${INTERMEDIARY_RESULTS_FOLDER}/name-mappings.json`
    );

    const rootElementName = args.baseElement ?? nameMappings.fullName;
    if (toSnakeCase(nameMappings.fullName) !== rootElementName) {
      throw new Error(
        `Base element name "${toSnakeCase(
          nameMappings.fullName
        )}" does not match the expected name "${rootElementName}".`
      );
    }

    // Transform JSON schema
    const transformedJsonSchema = transformJsonSchema(jsonSchema, nameMappings);
    writeFile(
      transformedJsonSchema,
      `${INTERMEDIARY_RESULTS_FOLDER}/transformed-schema.json`
    );

    const updatedNameMappings: XsdElement = replaceExtensionNames(
      nameMappings,
      JSON.parse(JSON.stringify(transformedJsonSchema, null, 2)) // Without stringify/parse, some inner elements are missing, not sure what's going on here.
    );
    writeFile(
      updatedNameMappings,
      `${INTERMEDIARY_RESULTS_FOLDER}/name-mappings.json`
    );

    // Extract a list of all fields from the JSON schema
    const allFields = getAllFieldsFromJSONSchema(
      transformedJsonSchema,
      nameMappings.abbrName
    );
    // Sort alphabetically
    allFields.sort((a: string, b: string) => a.localeCompare(b));
    writeFile(allFields, `${INTERMEDIARY_RESULTS_FOLDER}/all-fields.json`);

    // Expand the 'form-setup' to contain all nested fields
    const expandedFormSetup = expandFormSetup(formSetup, nameMappings);
    writeFile(
      expandedFormSetup,
      `${args.outputFolder}/expanded-form-setup.json`
    );

    // Verify that the form setup contains all fields from the JSON schema.
    // Also verify that the form setup does not contain any fields that are not in the JSON schema.
    verifyContainsAllFieldsFromSchema(expandedFormSetup, allFields);

    // Generate rules from transformed JSON schema
    const generatedRules = generateRules(transformedJsonSchema, nameMappings);
    writeFile(
      generatedRules,
      `${INTERMEDIARY_RESULTS_FOLDER}/generated-rules.json`
    );

    // Transform custom rules
    const transformedRules = transformRules(
      customRules,
      transformedJsonSchema,
      updatedNameMappings
    );
    writeFile(
      transformedRules,
      `${INTERMEDIARY_RESULTS_FOLDER}/transformed-custom-rules.json`
    );

    // Combine generated rules with custom rules
    const combinedRules = combineRules(generatedRules, transformedRules);
    validateRules(combinedRules);
    writeFile(
      combinedRules,
      `${INTERMEDIARY_RESULTS_FOLDER}/combined-rules.json`
    );

    const clientRules = filterClientRules(combinedRules);
    writeFile(clientRules, `${args.outputFolder}/client-rules.json`);

    const affectedFieldsForFieldValueChange =
      computeAffectedFieldsForFieldValueChange(clientRules);
    const affectedFields = mapToObject(affectedFieldsForFieldValueChange);
    writeFile(
      affectedFields,
      `${args.outputFolder}/affected-fields-for-field-value-change.json`
    );

    // Notify the user about cross-page dependencies
    findCrossPageDependencies(
      expandedFormSetup,
      affectedFieldsForFieldValueChange
    );
  } catch (error: unknown) {
    console.error(error);
    process.exit(1);
  }
}

main();
