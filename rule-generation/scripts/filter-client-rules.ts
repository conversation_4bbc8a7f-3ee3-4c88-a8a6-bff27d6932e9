import {
  BasicRule,
  Condition,
  ConditionalRule,
  isCondition,
  Rule,
  ServerOnlyRule,
} from '@helaba/iso20022-lib/rules';

function filterBasicRules(
  rules: BasicRule[],
  serverOnlyFields: string[]
): BasicRule[] {
  return rules
    .filter((rule) => !serverOnlyFields.includes(rule.target))
    .reduce<BasicRule[]>((acc, rule) => {
      if (rule.type !== 'contains') {
        acc.push(rule);
        return acc;
      }
      // Remove 'otherFields' entries from 'contains' rules that reference server-only fields.
      const filteredOtherFields = rule.value.filter(
        (field) => !serverOnlyFields.includes(field)
      );
      // If there are no other fields left after filtering, we remove the 'contains' rule entirely
      if (filteredOtherFields.length > 0) {
        acc.push({
          ...rule,
          value: filteredOtherFields,
        });
        return acc;
      }
      return acc;
    }, []);
}

function conditionReferencesServerOnlyField(
  condition: Condition,
  serverOnlyFields: string[]
): boolean {
  return (
    serverOnlyFields.includes(condition.field) ||
    (condition.type === 'notEqual' &&
      serverOnlyFields.includes(condition.otherField))
  );
}

export function filterClientRules(allRules: Rule[]): Rule[] {
  const serverOnlyRules: ServerOnlyRule[] = allRules.filter(
    (rule) => rule.type === 'serverOnly'
  );
  const serverOnlyFields: string[] = serverOnlyRules
    .filter((rule) => rule.value === true)
    .map((rule) => rule.target);

  const basicRules = allRules.filter(
    (rule) => rule.type !== 'serverOnly' && rule.type !== 'condition'
  );
  const filteredBasicRules = filterBasicRules(basicRules, serverOnlyFields);

  const conditionalRules = allRules.filter((rule) => rule.type === 'condition');
  const filteredConditionalRules = conditionalRules.reduce<ConditionalRule[]>(
    (acc, rule) => {
      // If any condition mentions a 'serverOnly' field, we remove the entire conditional rule. It will have to be checked on the server.
      if (
        rule.conditions.some((condition) =>
          isCondition(condition)
            ? conditionReferencesServerOnlyField(condition, serverOnlyFields)
            : condition.conditions.some((nestedCondition) =>
                conditionReferencesServerOnlyField(
                  nestedCondition,
                  serverOnlyFields
                )
              )
        )
      ) {
        return acc;
      }

      // Remove nested rules from conditional rules that target server-only fields
      const filteredRules = filterBasicRules(rule.rules, serverOnlyFields);

      // If there are no nested rules left after filtering, we remove the conditional rule entirely
      if (filteredRules.length > 0) {
        acc.push({
          ...rule,
          rules: filteredRules,
        });
      }
      return acc;
    },
    []
  );

  return [...filteredBasicRules, ...filteredConditionalRules];
}
