import { FormSetup, XsdElement } from './types';
import { isValidObject, isValidStringArray } from './utils';

function isValidObjectOfStringArrays(
  obj: unknown
): obj is Record<string, string[]> {
  return isValidObject(obj) && Object.values(obj).every(isValidStringArray);
}

function flattenNameMappings(nameMappings: XsdElement): XsdElement[] {
  if (nameMappings.children.length === 0) {
    return [nameMappings];
  }

  const flattened: XsdElement[] = [];
  for (const child of nameMappings.children) {
    const childMappings = flattenNameMappings(child);
    flattened.push(...childMappings);
  }

  return flattened;
}

function expandField(
  field: string,
  definitions: Record<string, string[]>,
  baseName: string
): string[] {
  if (!field.endsWith(']')) {
    return [baseName + '-' + field];
  }

  const fieldParts = field.split('[');
  const fieldName = fieldParts[0];
  const definitionReference = fieldParts[1].slice(0, -1); // Remove the closing bracket

  const definition = definitions[definitionReference];
  if (!definition || !isValidStringArray(definition)) {
    throw new Error(
      `Invalid definition reference '${definitionReference}' for field '${field}'`
    );
  }

  if (definition.length === 0) {
    return [baseName + '-' + fieldName];
  }

  const expandedFields: string[] = [];
  for (const def of definition) {
    expandedFields.push(
      ...expandField(`${fieldName}-${def}`, definitions, baseName)
    );
  }

  return expandedFields;
}

function addNames(
  fields: string[],
  flattenedNameMappings: XsdElement[]
): Record<string, string> {
  const result: Record<string, string> = {};
  for (const field of fields) {
    const nameMapping = flattenedNameMappings.find(
      (mapping) => mapping.nestedAbbrName === field
    );
    if (!nameMapping) {
      throw new Error(`No name mapping found for field '${field}'`);
    }
    result[field] = nameMapping.fullName;
  }
  return result;
}

export function expandFormSetup(
  formSetup: unknown,
  nameMappings: XsdElement
): FormSetup {
  if (!isValidObject(formSetup)) {
    throw new Error('Invalid form setup: must be a valid object');
  }

  if (!('pages' in formSetup) || !('definitions' in formSetup)) {
    throw new Error(
      "Invalid form setup: must contain 'pages' and 'definitions'"
    );
  }

  const pages = formSetup['pages'];
  if (!isValidObject(pages)) {
    throw new Error("Invalid form setup: 'pages' must be a valid object");
  }

  const definitions = formSetup['definitions'];
  if (!isValidObjectOfStringArrays(definitions)) {
    throw new Error(
      "Invalid form setup: 'definitions' must be a valid object with string arrays"
    );
  }

  const baseName = nameMappings.abbrName;
  const flattenedNameMappings: XsdElement[] = flattenNameMappings(nameMappings);

  const result: FormSetup = {};

  for (const [pageKey, page] of Object.entries(pages)) {
    if (!isValidObject(page)) {
      throw new Error(
        `Invalid page setup for '${pageKey}': must be a valid object`
      );
    }

    if (!('visible' in page) || !isValidStringArray(page['visible'])) {
      throw new Error(`Page '${pageKey}' must contain 'visible' field`);
    }

    const visibleFields = page['visible'];
    let serverOnlyFields: string[] = [];

    if ('serverOnly' in page) {
      if (!isValidStringArray(page['serverOnly'])) {
        throw new Error(
          `Page '${pageKey}' has a 'serverOnly' field that is not a string array`
        );
      }

      serverOnlyFields = page['serverOnly'];
    }

    let expandedVisibleFields: Record<string, string> = {};
    for (const field of visibleFields) {
      const expandedField: string[] = expandField(field, definitions, baseName);
      const expandedFieldWithNames: Record<string, string> = addNames(
        expandedField,
        flattenedNameMappings
      );
      expandedVisibleFields = {
        ...expandedVisibleFields,
        ...expandedFieldWithNames,
      };
    }

    let expandedServerOnlyFields: Record<string, string> = {};
    for (const field of serverOnlyFields) {
      const expandedField: string[] = expandField(field, definitions, baseName);
      const expandedFieldWithNames: Record<string, string> = addNames(
        expandedField,
        flattenedNameMappings
      );
      expandedServerOnlyFields = {
        ...expandedServerOnlyFields,
        ...expandedFieldWithNames,
      };
    }

    result[pageKey] = {
      visible: expandedVisibleFields,
      serverOnly: expandedServerOnlyFields,
    };
  }

  // Make sure there are no duplicate fields in the result
  verifiyNoDuplicateFields(result);

  return result;
}

function verifiyNoDuplicateFields(fields: FormSetup): void {
  const allFields: Set<string> = new Set();

  for (const pageKey in fields) {
    const page = fields[pageKey];
    const allPageFields = [
      ...Object.keys(page.visible),
      ...Object.keys(page.serverOnly),
    ];

    for (const field of allPageFields) {
      if (allFields.has(field)) {
        throw new Error(
          `Duplicate field found: '${field}' in page '${pageKey}'`
        );
      }
      allFields.add(field);
    }
  }
}

export function verifyContainsAllFieldsFromSchema(
  formSetupFields: FormSetup,
  allFields: string[]
): void {
  const allFormSetupFields = Object.values(formSetupFields).flatMap((page) => [
    ...Object.keys(page.visible),
    ...Object.keys(page.serverOnly),
  ]);

  for (const field of allFields) {
    if (!allFormSetupFields.includes(field)) {
      throw new Error(
        `Field '${field}' from schema is not present in the expanded form setup`
      );
    }
  }

  console.log('All fields from schema are present in the expanded form setup');

  for (const field of allFormSetupFields) {
    if (!allFields.includes(field)) {
      throw new Error(
        `Field '${field}' from form setup is not present in the schema fields`
      );
    }
  }
  console.log(
    'All fields from the expanded form setup are present in the schema fields'
  );
}
