import { FormSetup } from './types';

export function findCrossPageDependencies(
  formSetup: FormSetup,
  affectedFieldsForFieldValueChange: Map<string, Set<string>>
): void {
  for (const [pageKey, page] of Object.entries(formSetup)) {
    for (const field of Object.keys(page.visible)) {
      const affectedFields = affectedFieldsForFieldValueChange.get(field);
      if (!affectedFields) {
        continue;
      }

      for (const affectedField of affectedFields) {
        // Check if the affected field is in a different page
        for (const [otherPageKey, otherPage] of Object.entries(formSetup)) {
          if (otherPageKey === pageKey) {
            continue; // Skip the same page
          }

          if (Object.keys(otherPage.visible).includes(affectedField)) {
            console.warn(
              `Cross-page dependency detected: Field '${field}' on page '${pageKey}' affects field '${affectedField}' on page '${otherPageKey}'`
            );
          }
        }
      }
    }
  }
}
