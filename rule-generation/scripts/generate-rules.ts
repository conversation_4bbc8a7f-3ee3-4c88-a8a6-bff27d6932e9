import {
  Condition,
  ConditionalRule,
  PatternRule,
  RequiredRule,
  Rule,
} from '../../projects/iso20022-lib/rules';
import { XsdElement } from './types';
import {
  generateConditionalExplanation,
  GENERATED_RULE_PREFIX,
  generateRuleDescription,
  generateRuleId,
  getDefinition,
  getNestedFieldNames,
  getObjectProperties,
  getRef,
  getRequiredProperties,
  isValidObject,
} from './utils';

function getRequiredRule(
  nestedFieldName: string,
  conditionalExplanation: string | undefined,
  translatedConditionalExplanation: string | undefined,
  conditionalParentRuleId: string | undefined,
  nameMappings: XsdElement
): RequiredRule {
  return {
    id: generateRuleId('required', GENERATED_RULE_PREFIX, nestedFieldName, {
      isConditional:
        !!conditionalExplanation && !!translatedConditionalExplanation,
      conditionalParentRuleId,
    }),
    description: generateRuleDescription(
      'required',
      nestedFieldName,
      nameMappings,
      'en',
      {
        ruleDependentValue: true,
        conditionalExplanation,
      }
    ),
    descriptionTranslationProposal: generateRuleDescription(
      'required',
      nestedFieldName,
      nameMappings,
      'de',
      {
        ruleDependentValue: true,
        conditionalExplanation: translatedConditionalExplanation,
      }
    ),
    type: 'required',
    value: true,
    target: nestedFieldName,
  };
}

function getPatternRule(
  nestedFieldName: string,
  regex: string | undefined,
  enumValues: string[] | undefined,
  conditionalExplanation: string | undefined,
  translatedConditionalExplanation: string | undefined,
  conditionalParentRuleId: string | undefined,
  nameMappings: XsdElement
): PatternRule {
  // Escape special regex characters in the strings.
  const escapedEnumValues = enumValues?.map((value) =>
    value.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  );
  const joinedEnumValues = escapedEnumValues
    ? `^(${escapedEnumValues.join('|')})?$`
    : undefined;
  const pattern = regex || joinedEnumValues;

  if (!pattern) {
    throw new Error(
      `Invalid JSON schema: '${nestedFieldName}' has no valid pattern or enum values.`
    );
  }

  return {
    id: generateRuleId('pattern', GENERATED_RULE_PREFIX, nestedFieldName, {
      isConditional: !!conditionalExplanation,
      conditionalParentRuleId,
    }),
    description: generateRuleDescription(
      'pattern',
      nestedFieldName,
      nameMappings,
      'en',
      {
        ruleDependentValue: regex || enumValues,
        conditionalExplanation,
      }
    ),
    descriptionTranslationProposal: generateRuleDescription(
      'pattern',
      nestedFieldName,
      nameMappings,
      'de',
      {
        ruleDependentValue: regex || enumValues,
        conditionalExplanation: translatedConditionalExplanation,
      }
    ),
    type: 'pattern',
    value: pattern,
    target: nestedFieldName,
  };
}

function getRequiredOneOfs(
  definitions: Record<string, unknown>,
  ref: string,
  nestedFieldName: string
): Record<string, Record<string, unknown>[]> {
  const definition = getDefinition(definitions, ref);

  if (definition['type'] !== 'object') {
    return {};
  }

  const properties = definition['properties'];
  const oneOf = definition['oneOf'];

  if (!properties && !oneOf) {
    return {};
  }

  if (isValidObject(properties)) {
    const requiredProperties = getRequiredProperties(definition);
    if (!requiredProperties) {
      return {};
    }
    let requiredOneOfs: Record<string, Record<string, unknown>[]> = {};
    for (const requiredProperty of requiredProperties) {
      const property = properties[requiredProperty];
      if (!isValidObject(property)) {
        throw new Error(
          `Invalid property format at ${nestedFieldName}-${requiredProperty}.`
        );
      }
      const propertyRef = getRef(property);
      if (!propertyRef) {
        continue;
      }
      requiredOneOfs = {
        ...requiredOneOfs,
        ...getRequiredOneOfs(
          definitions,
          propertyRef,
          `${nestedFieldName}-${requiredProperty}`
        ),
      };
    }
    return requiredOneOfs;
  } else if (
    Array.isArray(oneOf) &&
    oneOf.every((entry) => isValidObject(entry))
  ) {
    if (!(oneOf.length > 1)) {
      return {};
    }
    return {
      [nestedFieldName]: oneOf,
    };
  }

  throw new Error(`Invalid properties or oneOf at ${nestedFieldName}.`);
}

function getNestedFieldNamesPerOneOfBranch(
  definitions: Record<string, unknown>,
  oneOf: Record<string, unknown>[],
  nestedFieldName: string
): Record<string, string[]> {
  const nestedFieldNamesPerBranch: Record<string, string[]> = {};

  for (const oneOfEntry of oneOf) {
    const data = getDataFromOneOfEntry(oneOfEntry);
    if (!data) {
      continue;
    }
    const { propertyKey, propertyRef } = data;

    const oneOfEntryNestedFieldName = `${nestedFieldName}-${propertyKey}`;

    const nestedFieldNames = getNestedFieldNames(
      definitions,
      propertyRef,
      oneOfEntryNestedFieldName
    );
    nestedFieldNamesPerBranch[propertyKey] = nestedFieldNames;
  }

  return nestedFieldNamesPerBranch;
}

function getNestedRequiredRulesPerOneOfBranch(
  definitions: Record<string, unknown>,
  oneOf: Record<string, unknown>[],
  nestedFieldName: string,
  conditionalExplanation: string | undefined,
  translatedConditionalExplanation: string | undefined,
  conditionalParentRuleId: string | undefined,
  nameMappings: XsdElement
): Record<string, RequiredRule[]> {
  const nestedRequiredRulesPerBranch: Record<string, RequiredRule[]> = {};

  for (const oneOfEntry of oneOf) {
    const data = getDataFromOneOfEntry(oneOfEntry);
    if (!data) {
      continue;
    }
    const { propertyKey, propertyRef } = data;

    const oneOfEntryNestedFieldName = `${nestedFieldName}-${propertyKey}`;

    const nestedRequiredRules: RequiredRule[] = getRules(
      definitions,
      propertyRef,
      oneOfEntryNestedFieldName,
      nameMappings,
      {
        globalRequired: true,
        includeOnlyRequiredRules: true,
        conditionalExplanation,
        translatedConditionalExplanation,
        conditionalParentRuleId,
      }
    ).filter((rule) => rule.type === 'required'); // Filter is only necessary so the "RequiredRule" type is correctly inferred.

    nestedRequiredRulesPerBranch[propertyKey] = nestedRequiredRules;
  }

  return nestedRequiredRulesPerBranch;
}

function getNestedRequiredOneOfsPerOneOfBranch(
  definitions: Record<string, unknown>,
  oneOf: Record<string, unknown>[],
  nestedFieldName: string
): Record<string, Record<string, Record<string, unknown>[]>> {
  const nestedRequiredOneOfsPerBranch: Record<
    string,
    Record<string, Record<string, unknown>[]>
  > = {};

  for (const oneOfEntry of oneOf) {
    const data = getDataFromOneOfEntry(oneOfEntry);
    if (!data) {
      continue;
    }
    const { propertyKey, propertyRef } = data;

    const oneOfEntryNestedFieldName = `${nestedFieldName}-${propertyKey}`;

    const nestedRequiredOneOfs: Record<string, Record<string, unknown>[]> =
      getRequiredOneOfs(definitions, propertyRef, oneOfEntryNestedFieldName);
    nestedRequiredOneOfsPerBranch[propertyKey] = nestedRequiredOneOfs;
  }

  return nestedRequiredOneOfsPerBranch;
}

// Construct conditional 'required' rules for the oneOf entries, requiring all required fields on one branch of the tree if all fields in the other branches are not set.
// This needs to make sure that oneOfs inside the branches are accounted for (they will not be included in the hypotheticalNestedRequiredRules)
function getOneOfConditionalRequiredRules(
  oneOf: Record<string, unknown>[],
  definitions: Record<string, unknown>,
  nestedFieldName: string,
  nameMappings: XsdElement
): ConditionalRule[] {
  const nestedFieldNamesPerBranch: Record<string, string[]> =
    getNestedFieldNamesPerOneOfBranch(definitions, oneOf, nestedFieldName);
  const hypotheticalNestedRequiredRulesPerBranch: Record<
    string,
    RequiredRule[]
  > = getNestedRequiredRulesPerOneOfBranch(
    definitions,
    oneOf,
    nestedFieldName,
    undefined, // Conditional explanations are added below.
    undefined, // Translated conditional explanations are added below.
    undefined, // Conditional parent nested field name is added below.
    nameMappings
  );
  // It is not enough to retrieve the hypotheticalNestedRequiredRules. This works if there are no nested 'oneOf's. For nested 'oneOf's, we cannot retrieve a plain 'required' rule. It's "either this or that". So we need to construct conditional rules in this case.
  // Contains for each branch (identified by the propertyKey of the branch's oneOf entry) and object that contains required oneOf entries on that branch. Each oneOf entry (Record<string, unknown>[]) is identified by its nestedFieldName.
  const hypotheticalNestedRequiredOneOfsPerBranch: Record<
    string,
    Record<string, Record<string, unknown>[]>
  > = getNestedRequiredOneOfsPerOneOfBranch(
    definitions,
    oneOf,
    nestedFieldName
  );
  if (
    Object.values(hypotheticalNestedRequiredOneOfsPerBranch).some(
      (nestedRequiredOneOfs) => Object.entries(nestedRequiredOneOfs).length > 0
    )
  ) {
    throw new Error(
      `There are nested required oneOfs starting from ${nestedFieldName}. There is some logic to be implemented here that adds the necessary rules to handle this edge case.`
    );
  }

  const conditionalRequiredRules: ConditionalRule[] = [];

  for (const [propertyKey, nestedRequiredRules] of Object.entries(
    hypotheticalNestedRequiredRulesPerBranch
  )) {
    // The condition for applying the required rules is that all fields in the other branches are not set.
    const fieldNamesOfOtherBranches: string[] = Object.entries(
      nestedFieldNamesPerBranch
    )
      .filter(([otherPropertyKey]) => otherPropertyKey !== propertyKey)
      .flatMap(([, nestedFieldNames]) => nestedFieldNames);
    const notPresentConditions: Condition[] = fieldNamesOfOtherBranches.map(
      (fieldName) => ({
        field: fieldName,
        type: 'present',
        value: false,
      })
    );

    const conditionalRequiredRulesForBranch: ConditionalRule[] =
      nestedRequiredRules.map((requiredRule) => {
        const conditionalParentRuleId = generateRuleId(
          'condition',
          GENERATED_RULE_PREFIX,
          requiredRule.target,
          {
            conditions: notPresentConditions,
            conditionalRuleType: 'required',
          }
        );
        const requiredRuleId = generateRuleId(
          'required',
          GENERATED_RULE_PREFIX,
          requiredRule.target,
          {
            isConditional: true,
            conditionalParentRuleId: conditionalParentRuleId,
          }
        );
        const conditionalRequiredRules = [
          {
            ...requiredRule,
            id: requiredRuleId,
            description: generateRuleDescription(
              'required',
              requiredRule.target,
              nameMappings,
              'en',
              {
                ruleDependentValue: true,
                conditionalExplanation: generateConditionalExplanation(
                  notPresentConditions,
                  'and',
                  'en',
                  nameMappings
                ),
              }
            ),
            descriptionTranslationProposal: generateRuleDescription(
              'required',
              requiredRule.target,
              nameMappings,
              'de',
              {
                ruleDependentValue: true,
                conditionalExplanation: generateConditionalExplanation(
                  notPresentConditions,
                  'and',
                  'de',
                  nameMappings
                ),
              }
            ),
          },
        ];
        return {
          id: conditionalParentRuleId,
          description: generateRuleDescription(
            'condition',
            requiredRule.target,
            nameMappings,
            'en',
            {
              conditions: notPresentConditions,
              conditionsConnector: 'and',
              conditionalRules: conditionalRequiredRules,
              conditionalRulesConnector: 'and',
            }
          ),
          descriptionTranslationProposal: generateRuleDescription(
            'condition',
            requiredRule.target,
            nameMappings,
            'de',
            {
              conditions: notPresentConditions,
              conditionsConnector: 'and',
              conditionalRules: conditionalRequiredRules,
              conditionalRulesConnector: 'and',
            }
          ),
          type: 'condition',
          target: requiredRule.target,
          conditions: notPresentConditions,
          conditionsConnector: 'and',
          rules: conditionalRequiredRules,
        };
      });

    conditionalRequiredRules.push(...conditionalRequiredRulesForBranch);
  }
  return conditionalRequiredRules;
}

function getDataFromOneOfEntry(oneOfEntry: Record<string, unknown>):
  | {
      propertyKey: string;
      propertyRef: string;
    }
  | undefined {
  const properties = oneOfEntry['properties'];
  if (!isValidObject(properties) || Object.keys(properties).length !== 1) {
    throw new Error(`Invalid properties format in oneOf entry: ${oneOfEntry}`);
  }

  const [propertyKey, propertyValue] = Object.entries(properties)[0];

  if (!isValidObject(propertyValue)) {
    throw new Error(`Invalid property format for "${propertyKey}".`);
  }

  const propertyRef = getRef(propertyValue);
  if (!propertyRef) {
    return undefined;
  }

  return {
    propertyKey,
    propertyRef,
  };
}

function getPrimitiveRules(
  type: unknown,
  definition: Record<string, unknown>,
  nestedFieldName: string,
  nameMappings: XsdElement,
  config: {
    required: boolean;
    includeOnlyRequiredRules: boolean;
    conditionalExplanation: string | undefined;
    translatedConditionalExplanation: string | undefined;
    conditionalParentRuleId: string | undefined;
  }
): Rule[] {
  const rules: Rule[] = [];

  if (!(type === 'string' || type === 'boolean')) {
    console.error(
      `Invalid JSON schema: '${nestedFieldName}' is of type ${type}. This is not supported as of now.`
    );
    return rules;
  }

  if (type === 'string') {
    const minLength = definition['minLength'];
    const maxLength = definition['maxLength'];
    const pattern = definition['pattern'];
    const enumValues = definition['enum'];

    let addedRequiredRule = false;

    if (minLength) {
      if (minLength !== 1) {
        console.error(
          `Invalid JSON schema: '${nestedFieldName}' has a minLength of ${minLength}, but only minLength of 1 is supported.`
        );
      } else if (config.required && !addedRequiredRule) {
        rules.push(
          getRequiredRule(
            nestedFieldName,
            config.conditionalExplanation,
            config.translatedConditionalExplanation,
            config.conditionalParentRuleId,
            nameMappings
          )
        );
        addedRequiredRule = true;
      }
    }

    if (maxLength) {
      if (typeof maxLength !== 'number') {
        console.error(
          `Invalid JSON schema: '${nestedFieldName}' has a maxLength of ${definition['maxLength']}, but only numeric maxLength is supported.`
        );
      } else if (!config.includeOnlyRequiredRules) {
        rules.push({
          id: generateRuleId(
            'maxLength',
            GENERATED_RULE_PREFIX,
            nestedFieldName,
            {
              isConditional: !!config.conditionalExplanation,
              conditionalParentRuleId: config.conditionalParentRuleId,
            }
          ),
          description: generateRuleDescription(
            'maxLength',
            nestedFieldName,
            nameMappings,
            'en',
            {
              ruleDependentValue: definition['maxLength'],
              conditionalExplanation: config.conditionalExplanation,
            }
          ),
          descriptionTranslationProposal: generateRuleDescription(
            'maxLength',
            nestedFieldName,
            nameMappings,
            'de',
            {
              ruleDependentValue: definition['maxLength'],
              conditionalExplanation: config.conditionalExplanation,
            }
          ),
          type: 'maxLength',
          value: maxLength,
          target: nestedFieldName,
        });
      }
    }

    if (pattern) {
      if (typeof pattern !== 'string') {
        console.error(
          `Invalid JSON schema: '${nestedFieldName}' has a pattern of ${pattern}, but only string patterns are supported.`
        );
      } else {
        if (!config.includeOnlyRequiredRules) {
          rules.push(
            getPatternRule(
              nestedFieldName,
              pattern,
              undefined,
              config.conditionalExplanation,
              config.translatedConditionalExplanation,
              config.conditionalParentRuleId,
              nameMappings
            )
          );
        }
        // This is an assumption we make: If a pattern is defined and the element is required, the field is required, even though the pattern may allow for the empty string.
        if (config.required && !addedRequiredRule) {
          rules.push(
            getRequiredRule(
              nestedFieldName,
              config.conditionalExplanation,
              config.translatedConditionalExplanation,
              config.conditionalParentRuleId,
              nameMappings
            )
          );
          addedRequiredRule = true;
        }
      }
    }

    if (enumValues) {
      if (
        !Array.isArray(enumValues) ||
        !enumValues.every((v) => typeof v === 'string')
      ) {
        console.error(
          `Invalid JSON schema: '${nestedFieldName}' has an enum of ${enumValues}, but only string array enums are supported.`
        );
      } else {
        if (!config.includeOnlyRequiredRules) {
          rules.push(
            getPatternRule(
              nestedFieldName,
              undefined,
              enumValues,
              config.conditionalExplanation,
              config.translatedConditionalExplanation,
              config.conditionalParentRuleId,
              nameMappings
            )
          );
        }
        if (config.required && !addedRequiredRule) {
          rules.push(
            getRequiredRule(
              nestedFieldName,
              config.conditionalExplanation,
              config.translatedConditionalExplanation,
              config.conditionalParentRuleId,
              nameMappings
            )
          );
          addedRequiredRule = true;
        }
      }
    }
  } else if (definition['type'] === 'boolean') {
    const enumValues = ['true', 'false'];
    if (!config.includeOnlyRequiredRules) {
      rules.push(
        getPatternRule(
          nestedFieldName,
          undefined,
          enumValues,
          config.conditionalExplanation,
          config.translatedConditionalExplanation,
          config.conditionalParentRuleId,
          nameMappings
        )
      );
    }
    if (config.required) {
      rules.push(
        getRequiredRule(
          nestedFieldName,
          config.conditionalExplanation,
          config.translatedConditionalExplanation,
          config.conditionalParentRuleId,
          nameMappings
        )
      );
    }
  }

  return rules;
}

function getArrayRules(
  definitions: Record<string, unknown>,
  propertyValue: Record<string, unknown>,
  propertyRef: string,
  fieldName: string,
  nameMappings: XsdElement,
  config: {
    required: boolean;
    includeOnlyRequiredRules: boolean;
    conditionalExplanation: string | undefined;
    translatedConditionalExplanation: string | undefined;
    conditionalParentRuleId: string | undefined;
  }
): Rule[] {
  // In addition to the MaxItemsRule that is required if 'maxItems' is defined, we generate nested rules. The logic for what exactly these mean for the array case, sits in the FormRulesDirective.
  const arrayRules: Rule[] = [];

  // MaxItemsRule
  const maxItems = propertyValue['maxItems'];
  if (maxItems) {
    if (typeof maxItems !== 'number') {
      throw new Error(
        `Invalid JSON schema: '${fieldName}' has a maxItems of ${maxItems}, but only numeric maxItems is supported.`
      );
    }

    if (!config.includeOnlyRequiredRules) {
      arrayRules.push({
        id: generateRuleId('maxItems', GENERATED_RULE_PREFIX, fieldName),
        description: generateRuleDescription(
          'maxItems',
          fieldName,
          nameMappings,
          'en',
          {
            ruleDependentValue: maxItems,
            conditionalExplanation: config.conditionalExplanation,
          }
        ),
        descriptionTranslationProposal: generateRuleDescription(
          'maxItems',
          fieldName,
          nameMappings,
          'de',
          {
            ruleDependentValue: maxItems,
            conditionalExplanation: config.conditionalExplanation,
          }
        ),
        type: 'maxItems',
        value: maxItems,
        target: fieldName,
      });
    }
  }

  // Nested rules excluding RequiredRules for individual items' fields
  arrayRules.push(
    ...getRules(definitions, propertyRef, fieldName, nameMappings, {
      globalRequired: config.required,
      includeOnlyRequiredRules: config.includeOnlyRequiredRules,
      conditionalExplanation: config.conditionalExplanation,
      translatedConditionalExplanation: config.translatedConditionalExplanation,
      conditionalParentRuleId: config.conditionalParentRuleId,
    })
  );

  return arrayRules;
}

function getObjectPropertyRules(
  definitions: Record<string, unknown>,
  propertyKey: string,
  propertyValue: Record<string, unknown>,
  nestedFieldName: string,
  nameMappings: XsdElement,
  config: {
    required: boolean;
    includeOnlyRequiredRules: boolean;
    conditionalExplanation: string | undefined;
    translatedConditionalExplanation: string | undefined;
    conditionalParentRuleId: string | undefined;
  }
): Rule[] {
  const objectPropertyRules: Rule[] = [];

  const propertyRef = getRef(propertyValue);

  if (!propertyRef) {
    // There might be a primitive rule defined here that is not extracted into its own definition.
    objectPropertyRules.push(
      ...getPrimitiveRules(
        propertyValue['type'],
        propertyValue,
        nestedFieldName,
        nameMappings,
        {
          required: config.required,
          includeOnlyRequiredRules: config.includeOnlyRequiredRules,
          conditionalExplanation: config.conditionalExplanation,
          translatedConditionalExplanation:
            config.translatedConditionalExplanation,
          conditionalParentRuleId: config.conditionalParentRuleId,
        }
      )
    );
    return objectPropertyRules;
  }

  if ('type' in propertyValue) {
    if (propertyValue['type'] !== 'array') {
      throw new Error(
        `Invalid JSON schema: '${propertyKey}' is of type ${propertyValue['type']} and there is an additional ref to ${propertyRef}. This is not supported as of now.`
      );
    }
    objectPropertyRules.push(
      ...getArrayRules(
        definitions,
        propertyValue,
        propertyRef,
        nestedFieldName,
        nameMappings,
        {
          required: config.required,
          includeOnlyRequiredRules: config.includeOnlyRequiredRules,
          conditionalExplanation: config.conditionalExplanation,
          translatedConditionalExplanation:
            config.translatedConditionalExplanation,
          conditionalParentRuleId: config.conditionalParentRuleId,
        }
      )
    );
  } else {
    // Standard case: no 'type' defined, but a ref to another definition.
    objectPropertyRules.push(
      ...getRules(definitions, propertyRef, nestedFieldName, nameMappings, {
        globalRequired: config.required,
        includeOnlyRequiredRules: config.includeOnlyRequiredRules,
        conditionalExplanation: config.conditionalExplanation,
        translatedConditionalExplanation:
          config.translatedConditionalExplanation,
        conditionalParentRuleId: config.conditionalParentRuleId,
      })
    );
  }

  return objectPropertyRules;
}

function getConditionalRulesForRequiredOneOf(
  definitions: Record<string, unknown>,
  nestedFieldName: string,
  requiredOneOf: Record<string, unknown>[],
  orPresentConditions: Condition[],
  nameMappings: XsdElement
): ConditionalRule[] {
  const conditionalExplanation = generateConditionalExplanation(
    orPresentConditions,
    'or',
    'en',
    nameMappings
  );
  const translatedConditionalExplanation = generateConditionalExplanation(
    orPresentConditions,
    'or',
    'de',
    nameMappings
  );

  // Already generate the parent rule ID here, so we can add it to the rule IDs of the nested "required" rules.
  const conditionalParentRuleId = generateRuleId(
    'condition',
    GENERATED_RULE_PREFIX,
    nestedFieldName,
    { conditions: orPresentConditions, conditionalRuleType: 'required' }
  );

  // Retrieve required rules for both sides.
  const nestedRequiredRulesPerBranch: Record<string, RequiredRule[]> =
    getNestedRequiredRulesPerOneOfBranch(
      definitions,
      requiredOneOf,
      nestedFieldName,
      conditionalExplanation,
      translatedConditionalExplanation,
      conditionalParentRuleId,
      nameMappings
    );

  // Retrieve required oneOfs for both sides.
  const nestedRequiredOneOfsPerBranch: Record<
    string,
    Record<string, Record<string, unknown>[]>
  > = getNestedRequiredOneOfsPerOneOfBranch(
    definitions,
    requiredOneOf,
    nestedFieldName
  );

  // Just a sanity check
  const numberOfOneOfOptions = Object.keys(requiredOneOf).length;
  if (numberOfOneOfOptions < 2) {
    throw new Error(`Unexpected oneOf with ${numberOfOneOfOptions} entries.`);
  }
  if (
    Object.keys(nestedRequiredRulesPerBranch).length !== numberOfOneOfOptions ||
    Object.keys(nestedRequiredOneOfsPerBranch).length !== numberOfOneOfOptions
  ) {
    throw new Error(`Unexpected number of nested entries.`);
  }

  const hasNestedRequiredRules = Object.values(
    nestedRequiredRulesPerBranch
  ).every(
    (listOfRequiredRulesForBranch) => listOfRequiredRulesForBranch.length > 0
  );
  const hasNestedRequiredOneOfs = Object.values(
    nestedRequiredOneOfsPerBranch
  ).every(
    (listOfRequiredOneOfsForBranch) =>
      Object.keys(listOfRequiredOneOfsForBranch).length > 0
  );

  // If there are no required rules and no required oneOfs, no rule needs to be constructed.
  if (!hasNestedRequiredRules && !hasNestedRequiredOneOfs) {
    return [];
  }

  // If there is exactly one required rule per side and there are no required oneOfs, we can construct a conditional rule using the original orPresentConditions and stick the required rules in there with an "or" rulesConnector.
  if (
    Object.values(nestedRequiredRulesPerBranch).every(
      (listOfRequiredRulesForBranch) =>
        listOfRequiredRulesForBranch.length === 1
    ) &&
    !hasNestedRequiredOneOfs
  ) {
    const conditionalRequiredRules = Object.values(
      nestedRequiredRulesPerBranch
    ).map((listOfRequiredRulesForBranch) => listOfRequiredRulesForBranch[0]);
    return [
      {
        id: conditionalParentRuleId,
        description: generateRuleDescription(
          'condition',
          nestedFieldName,
          nameMappings,
          'en',
          {
            conditions: orPresentConditions,
            conditionsConnector: 'or',
            conditionalRules: conditionalRequiredRules,
            conditionalRulesConnector: 'or',
          }
        ),
        descriptionTranslationProposal: generateRuleDescription(
          'condition',
          nestedFieldName,
          nameMappings,
          'de',
          {
            conditions: orPresentConditions,
            conditionsConnector: 'or',
            conditionalRules: conditionalRequiredRules,
            conditionalRulesConnector: 'or',
          }
        ),
        type: 'condition',
        conditions: orPresentConditions,
        conditionsConnector: 'or',
        rules: conditionalRequiredRules,
        rulesConnector: 'or',
      },
    ];
  }

  // There are a lot more options here that are just not present in the JSON schema we are currently handling (pacs.008).
  // However, there might be more logic to implement here in the future.
  // See concepts/required-one-of-conditional-rules.drawio for some considerations. Only the green ones are implemented so far.
  throw new Error(
    `Unsupported combination of nested required rules and nested required oneOfs for ${nestedFieldName}.`
  );
}

function getConditionalRequiredRulesForNotGloballyRequiredProperty(
  definitions: Record<string, unknown>,
  objectProperties: Record<string, unknown>,
  requiredPropertyKey: string,
  nestedFieldName: string,
  nameMappings: XsdElement
): ConditionalRule[] {
  const requiredProperty = objectProperties[requiredPropertyKey];
  if (!isValidObject(requiredProperty)) {
    throw new Error(
      `Invalid JSON schema: '${requiredPropertyKey}' is not a valid object`
    );
  }
  const otherProperties = Object.entries(objectProperties).filter(
    ([key]) => key !== requiredPropertyKey
  );
  const otherNestedFieldNames = otherProperties.reduce<string[]>(
    (acc, [propertyKey, propertyValue]) => {
      if (!isValidObject(propertyValue)) {
        throw new Error(
          `Invalid JSON schema: '${propertyKey}' is not a valid object`
        );
      }
      const propertyNestedFieldName = `${nestedFieldName}-${propertyKey}`;
      const propertyRef = getRef(propertyValue);
      if (!propertyRef) {
        acc.push(propertyNestedFieldName);
        return acc;
      }
      acc.push(
        ...getNestedFieldNames(
          definitions,
          propertyRef,
          propertyNestedFieldName
        )
      );
      return acc;
    },
    []
  );
  const presentConditions: Condition[] = otherNestedFieldNames.map(
    (fieldName) => ({
      field: fieldName,
      type: 'present',
      value: true,
    })
  );
  if (presentConditions.length === 0) {
    // There are no conditions. It doesn't make sense to construct a rule in that case.
    return [];
  }

  const conditionalExplanation = generateConditionalExplanation(
    presentConditions,
    'or',
    'en',
    nameMappings
  );
  const translatedConditionalExplanation = generateConditionalExplanation(
    presentConditions,
    'or',
    'de',
    nameMappings
  );

  const requiredPropertyRef = getRef(requiredProperty);
  const requiredPropertyNestedFieldName = `${nestedFieldName}-${requiredPropertyKey}`;

  const conditionalParentRuleId = generateRuleId(
    'condition',
    GENERATED_RULE_PREFIX,
    requiredPropertyNestedFieldName,
    { conditions: presentConditions, conditionalRuleType: 'required' }
  );
  const requiredRules: RequiredRule[] = !requiredPropertyRef
    ? [
        getRequiredRule(
          requiredPropertyNestedFieldName,
          conditionalExplanation,
          translatedConditionalExplanation,
          conditionalParentRuleId,
          nameMappings
        ),
      ]
    : getRules(
        definitions,
        requiredPropertyRef,
        requiredPropertyNestedFieldName,
        nameMappings,
        {
          globalRequired: true,
          includeOnlyRequiredRules: true,
          conditionalExplanation,
          translatedConditionalExplanation,
          conditionalParentRuleId,
        }
      ).filter((rule) => rule.type === 'required');

  // Check if there are nested oneOf rules. E.g. "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct" is treated in this function.
  // "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct" is not globally required and "SttlmAcct-Id" is the only required property.
  // It defines a choice between "SttlmAcct-Id-IBAN" and "SttlmAcct-Id-Othr" but we did not get any of these from the 'getRules' above ('globalRequired' is always false for 'oneOf').
  // And we also wouldn't know if the required rules we got above were connected by "or" or "and". The way 'getRules' is implemented, it is always an "and", so we treat the 'oneOf' case with "or" separately.
  const nestedRequiredOneOfs: Record<string, Record<string, unknown>[]> =
    !requiredPropertyRef
      ? {}
      : getRequiredOneOfs(
          definitions,
          requiredPropertyRef,
          requiredPropertyNestedFieldName
        );

  if (Object.entries(nestedRequiredOneOfs).length > 1) {
    throw new Error(
      `There is more than one nested required oneOf at ${requiredPropertyNestedFieldName}. This is not supported as of now.`
    );
  }

  if (
    Object.entries(nestedRequiredOneOfs).length === 0 &&
    requiredRules.length === 0
  ) {
    // Nothing is really required for the required property, so we just don't add any rules in that case.
    return [];
  }

  if (Object.entries(nestedRequiredOneOfs).length === 0) {
    return [
      {
        id: conditionalParentRuleId,
        description: generateRuleDescription(
          'condition',
          requiredPropertyNestedFieldName,
          nameMappings,
          'en',
          {
            conditions: presentConditions,
            conditionsConnector: 'or',
            conditionalRules: requiredRules,
            conditionalRulesConnector: 'and',
          }
        ),
        descriptionTranslationProposal: generateRuleDescription(
          'condition',
          requiredPropertyNestedFieldName,
          nameMappings,
          'de',
          {
            conditions: presentConditions,
            conditionsConnector: 'or',
            conditionalRules: requiredRules,
            conditionalRulesConnector: 'and',
          }
        ),
        type: 'condition',
        conditions: presentConditions,
        conditionsConnector: 'or',
        rules: requiredRules,
        rulesConnector: 'and',
      },
    ];
  }

  if (requiredRules.length === 0) {
    const [requiredOneOfNestedFieldName, requiredOneOf] =
      Object.entries(nestedRequiredOneOfs)[0];
    return getConditionalRulesForRequiredOneOf(
      definitions,
      requiredOneOfNestedFieldName,
      requiredOneOf,
      presentConditions,
      nameMappings
    );
  }

  throw new Error(
    `Unexpected combination of required rules and required oneOfs: ${requiredRules}, ${nestedRequiredOneOfs}`
  );
}

function getObjectPropertiesRules(
  definitions: Record<string, unknown>,
  definition: Record<string, unknown>,
  nestedFieldName: string,
  nameMappings: XsdElement,
  config: {
    required: boolean;
    includeOnlyRequiredRules: boolean;
    conditionalExplanation: string | undefined;
    translatedConditionalExplanation: string | undefined;
    conditionalParentRuleId: string | undefined;
  }
): Rule[] {
  const objectProperties = getObjectProperties(definition);
  const requiredProperties = getRequiredProperties(definition);

  const objectPropertiesRules: Rule[] = [];

  for (const [propertyKey, propertyValue] of Object.entries(objectProperties)) {
    if (!isValidObject(propertyValue)) {
      throw new Error(
        `Invalid JSON schema: '${propertyKey}' is not a valid object`
      );
    }

    objectPropertiesRules.push(
      ...getObjectPropertyRules(
        definitions,
        propertyKey,
        propertyValue,
        `${nestedFieldName}-${propertyKey}`,
        nameMappings,
        {
          required:
            config.required &&
            (requiredProperties?.includes(propertyKey) ?? false),
          includeOnlyRequiredRules: config.includeOnlyRequiredRules,
          conditionalExplanation: config.conditionalExplanation,
          translatedConditionalExplanation:
            config.translatedConditionalExplanation,
          conditionalParentRuleId: config.conditionalParentRuleId,
        }
      )
    );
  }

  // Add conditional required rules for all required properties if the current level is not globally required (for each required property, there must be a rule that requires it if any of the other fields are set). In the 'globally required' case, we create the basic required rules directly above.
  if (
    !config.required &&
    requiredProperties &&
    !config.includeOnlyRequiredRules &&
    Object.entries(objectProperties).length > 1 // This only makes sense if there is more than one property.
  ) {
    for (const requiredPropertyKey of requiredProperties) {
      objectPropertiesRules.push(
        ...getConditionalRequiredRulesForNotGloballyRequiredProperty(
          definitions,
          objectProperties,
          requiredPropertyKey,
          nestedFieldName,
          nameMappings
        )
      );
    }
  }

  return objectPropertiesRules;
}

function getRules(
  definitions: Record<string, unknown>,
  ref: string,
  nestedFieldName: string,
  nameMappings: XsdElement,
  config: {
    globalRequired: boolean;
    includeOnlyRequiredRules: boolean;
    conditionalExplanation: string | undefined;
    translatedConditionalExplanation: string | undefined;
    conditionalParentRuleId: string | undefined;
  }
): Rule[] {
  const definition = getDefinition(definitions, ref);

  const rules: Rule[] = [];

  if (definition['type'] === 'string' || definition['type'] === 'boolean') {
    rules.push(
      ...getPrimitiveRules(
        definition['type'],
        definition,
        nestedFieldName,
        nameMappings,
        {
          required: config.globalRequired,
          includeOnlyRequiredRules: config.includeOnlyRequiredRules,
          conditionalExplanation: config.conditionalExplanation,
          translatedConditionalExplanation:
            config.translatedConditionalExplanation,
          conditionalParentRuleId: config.conditionalParentRuleId,
        }
      )
    );
  } else if (definition['type'] === 'array') {
    throw new Error(
      `Array type on definition level is not supported. Error at field ${nestedFieldName}.`
    );
  } else if (definition['type'] === 'object') {
    const properties = definition['properties'];
    const oneOf = definition['oneOf'];

    if (!properties && !oneOf) {
      return rules;
    }

    const nestedRules: Rule[] = [];

    if (isValidObject(properties)) {
      nestedRules.push(
        ...getObjectPropertiesRules(
          definitions,
          definition,
          nestedFieldName,
          nameMappings,
          {
            required: config.globalRequired,
            includeOnlyRequiredRules: config.includeOnlyRequiredRules,
            conditionalExplanation: config.conditionalExplanation,
            translatedConditionalExplanation:
              config.translatedConditionalExplanation,
            conditionalParentRuleId: config.conditionalParentRuleId,
          }
        )
      );
    } else if (Array.isArray(oneOf)) {
      if (!oneOf.every((entry) => isValidObject(entry))) {
        throw new Error(`Invalid oneOf entry in oneOf ${oneOf}`);
      }

      for (const oneOfEntry of oneOf) {
        const data = getDataFromOneOfEntry(oneOfEntry);

        if (!data) {
          continue;
        }

        const { propertyKey, propertyRef } = data;

        nestedRules.push(
          ...getRules(
            definitions,
            propertyRef,
            `${nestedFieldName}-${propertyKey}`,
            nameMappings,
            {
              globalRequired: oneOf.length > 1 ? false : config.globalRequired, // 'oneOf' usually means that the sub properties are not globally required. However, if there is only one entry in the 'oneOf' array, the entry might be required.
              includeOnlyRequiredRules: config.includeOnlyRequiredRules,
              conditionalExplanation: config.conditionalExplanation,
              translatedConditionalExplanation:
                config.translatedConditionalExplanation,
              conditionalParentRuleId: config.conditionalParentRuleId,
            }
          )
        );
      }

      if (config.globalRequired && !config.includeOnlyRequiredRules) {
        // We need to construct conditional 'required' rules for the oneOf entries, requiring all required fields on one branch of the tree if all fields in the other branches are not set.
        nestedRules.push(
          ...getOneOfConditionalRequiredRules(
            oneOf,
            definitions,
            nestedFieldName,
            nameMappings
          )
        );
      }

      if (!config.includeOnlyRequiredRules) {
        // We could also construct conditional 'prohibited' rules for the oneOf entries, prohibiting all fields on one branch of the tree if at least one field in another branch is set.
        // However, this produces a lot of rules and is not strictly necessary as the client code automatically wipes the other branch's values when switching.
      }
    }

    rules.push(...nestedRules);
  } else {
    console.error(
      `Invalid JSON schema: '${nestedFieldName}' is of type ${definition['type']}. This is not supported as of now.`
    );
  }

  return rules;
}

export function generateRules(
  jsonSchema: Record<string, unknown>,
  nameMappings: XsdElement
): Rule[] {
  const basePropertyAbbreviatedName = nameMappings.abbrName;
  if (!('properties' in jsonSchema && 'definitions' in jsonSchema)) {
    throw new Error(
      "Invalid JSON schema: 'properties' and 'definitions' not found"
    );
  }

  const properties = jsonSchema['properties'];
  if (!isValidObject(properties)) {
    throw new Error("Invalid JSON schema: 'properties' is not a valid object");
  }

  const definitions = jsonSchema['definitions'];
  if (!isValidObject(definitions)) {
    throw new Error("Invalid JSON schema: 'definitions' is not a valid object");
  }

  const baseProperty = properties[basePropertyAbbreviatedName];
  if (!isValidObject(baseProperty)) {
    throw new Error(
      `Invalid JSON schema: '${basePropertyAbbreviatedName}' is not a valid object`
    );
  }

  const basePropertyRef = getRef(baseProperty);
  if (!basePropertyRef) {
    throw new Error(
      `Invalid JSON schema: '${basePropertyAbbreviatedName}' does not have a valid reference`
    );
  }

  return getRules(
    definitions,
    basePropertyRef,
    basePropertyAbbreviatedName,
    nameMappings,
    {
      globalRequired: true,
      includeOnlyRequiredRules: false,
      conditionalExplanation: undefined,
      translatedConditionalExplanation: undefined,
      conditionalParentRuleId: undefined,
    }
  );
}
