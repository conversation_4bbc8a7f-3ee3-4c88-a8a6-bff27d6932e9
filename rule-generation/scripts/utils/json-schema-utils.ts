import { splitPascalCase } from '../../../projects/iso20022-lib/util';
import { isValidObject, isValidStringArray } from './type-utils';

/**
 * Converts a string to snake case.
 * @param str the string to convert to snake case.
 * @returns the snake case string.
 * @example
 * toSnakeCase("GroupHeader") // "group_header"
 * toSnakeCase("FIToFICustomerCreditTransferV08") // "fi_to_fi_customer_credit_transfer_v08"
 * toSnakeCase("CreditTransferTransactionInformation") // "credit_transfer_transaction_information"
 * toSnakeCase("PreviousInstructingAgent1Account") // "previous_instructing_agent1_account"
 * toSnakeCase("IBAN") // "iban"
 */
export function toSnakeCase(str: string): string {
  const words = splitPascalCase(str);
  return words.map((word) => word.toLowerCase()).join('_');
}

export function getRef(property: Record<string, unknown>): string | undefined {
  const ref =
    property['$ref'] ||
    (isValidObject(property['items']) ? property['items']['$ref'] : undefined);
  return ref && typeof ref === 'string' ? ref.split('/').pop() : undefined;
}

export function getNestedFieldNames(
  definitions: Record<string, unknown>,
  ref: string,
  nestedFieldName: string
): string[] {
  const definition = definitions[ref];
  if (!isValidObject(definition)) {
    throw new Error(`Invalid JSON schema: '${ref}' is not a valid object`);
  }

  if (definition['type'] === 'string' || definition['type'] === 'boolean') {
    return [nestedFieldName];
  }

  if (definition['type'] === 'array') {
    console.log(
      `Array type on definition level is not supported. Skipping field ${nestedFieldName}.`
    );
    return [];
  }

  const nestedFieldNames: string[] = [];

  if (definition['type'] === 'object') {
    const properties = definition['properties'];
    const oneOf = definition['oneOf'];

    if (!properties && !oneOf) {
      return [];
    }

    if (isValidObject(properties)) {
      for (const [propertyKey, propertyValue] of Object.entries(properties)) {
        if (!isValidObject(propertyValue)) {
          throw new Error(
            `Invalid JSON schema: '${propertyKey}' is not a valid object`
          );
        }

        const propertyRef = getRef(propertyValue);
        if (!propertyRef) {
          nestedFieldNames.push(`${nestedFieldName}-${propertyKey}`);
          continue;
        }

        nestedFieldNames.push(
          ...getNestedFieldNames(
            definitions,
            propertyRef,
            `${nestedFieldName}-${propertyKey}`
          )
        );
      }
    } else if (Array.isArray(oneOf)) {
      for (const oneOfEntry of oneOf) {
        const properties = oneOfEntry.properties;
        if (
          !isValidObject(properties) ||
          Object.keys(properties).length !== 1
        ) {
          throw new Error(
            `Invalid properties format in oneOf entry: ${oneOfEntry}`
          );
        }

        const [propertyKey, propertyValue] = Object.entries(properties)[0];

        if (!isValidObject(propertyValue)) {
          throw new Error(`Invalid property format for "${propertyKey}".`);
        }

        const propertyRef = getRef(propertyValue);
        if (!propertyRef) {
          continue;
        }

        nestedFieldNames.push(
          ...getNestedFieldNames(
            definitions,
            propertyRef,
            `${nestedFieldName}-${propertyKey}`
          )
        );
      }
    }
  }
  return nestedFieldNames;
}

export function getDefinition(
  definitions: Record<string, unknown>,
  ref: string
): Record<string, unknown> {
  const definition = definitions[ref];
  if (!isValidObject(definition)) {
    throw new Error(`Invalid JSON schema: '${ref}' is not a valid object`);
  }

  return definition;
}

export function getRequiredProperties(
  definition: Record<string, unknown>
): string[] | undefined {
  const requiredProperties = definition['required'];
  if (
    !(
      typeof requiredProperties === 'undefined' ||
      isValidStringArray(requiredProperties)
    )
  ) {
    throw new Error(
      `Invalid required properties format for "${definition}". Expected undefined or an array.`
    );
  }

  return requiredProperties;
}

export function getObjectProperties(
  definition: Record<string, unknown>
): Record<string, unknown> {
  const objectProperties = definition['properties'];
  if (!isValidObject(objectProperties)) {
    throw new Error(
      `Invalid JSON schema: '${definition}' does not have valid object properties`
    );
  }
  return objectProperties;
}
