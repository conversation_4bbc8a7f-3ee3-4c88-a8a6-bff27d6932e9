import { XsdElement } from './types';
import {
  getRef,
  isValidObject,
  isValidStringArray,
  toSnakeCase,
} from './utils';

function getMergedDefinition(
  definitions: Record<string, unknown>[]
): Record<string, unknown> {
  const { properties, oneOf, required, ...rest } = definitions[0];

  const mergedNestedFieldNames: Set<string> = new Set();
  const mergedProperties: Record<string, Record<string, unknown>> = {};
  const mergedOneOf: Record<string, unknown>[] = [];

  for (const definition of definitions) {
    const nestedFieldNames = definition['nestedFieldNames'];
    const definitionProperties = definition['properties'];
    const definitionOneOf = definition['oneOf'];
    if (isValidStringArray(nestedFieldNames)) {
      for (const nestedFieldName of nestedFieldNames) {
        mergedNestedFieldNames.add(nestedFieldName);
      }
    }
    if (isValidObject(definitionProperties)) {
      for (const [propertyKey, propertyValue] of Object.entries(
        definitionProperties
      )) {
        if (!isValidObject(propertyValue)) {
          throw new Error(`Invalid property format for "${propertyKey}".`);
        }

        const existingProperty = mergedProperties[propertyKey];
        if (existingProperty) {
          // Merge nestedFieldNames
          existingProperty['nestedFieldNames'] = [
            ...(isValidStringArray(existingProperty['nestedFieldNames'])
              ? existingProperty['nestedFieldNames']
              : []),
            ...(isValidStringArray(propertyValue['nestedFieldNames'])
              ? propertyValue['nestedFieldNames']
              : []),
          ];
        } else {
          mergedProperties[propertyKey] = {
            ...propertyValue,
          };
        }
      }
    }
    if (Array.isArray(definitionOneOf)) {
      for (const oneOfEntry of definitionOneOf) {
        if (
          !isValidObject(oneOfEntry) ||
          !isValidObject(oneOfEntry['properties'])
        ) {
          throw new Error(`Invalid oneOf entry: ${oneOfEntry}`);
        }
        const propertyKey = Object.keys(oneOfEntry['properties'])[0];
        if (!propertyKey) {
          throw new Error(
            `No property key found in oneOf entry: ${oneOfEntry}`
          );
        }
        if (!isValidObject(oneOfEntry['properties'][propertyKey])) {
          throw new Error(
            `Invalid property format for "${propertyKey}" in oneOf entry.`
          );
        }

        const existingOneOf = mergedOneOf.find(
          (entry) =>
            isValidObject(entry['properties']) &&
            Object.keys(entry['properties'])[0] === propertyKey
        );

        if (!existingOneOf) {
          mergedOneOf.push(oneOfEntry);
        } else {
          if (
            !isValidObject(existingOneOf['properties']) ||
            !isValidObject(existingOneOf['properties'][propertyKey])
          ) {
            throw new Error(
              `Invalid existing oneOf entry for property "${propertyKey}".`
            );
          }
          let mergedNestedFieldNames =
            existingOneOf['properties'][propertyKey]['nestedFieldNames'];
          if (!isValidStringArray(mergedNestedFieldNames)) {
            throw new Error(
              `Invalid nestedFieldNames for property "${propertyKey}" in existing oneOf entry.`
            );
          }

          const newNestedFieldNames =
            oneOfEntry['properties'][propertyKey]['nestedFieldNames'];
          if (!isValidStringArray(newNestedFieldNames)) {
            throw new Error(
              `Invalid nestedFieldNames for property "${propertyKey}" in new oneOf entry.`
            );
          }

          // Merge nestedFieldNames
          mergedNestedFieldNames = [
            ...mergedNestedFieldNames,
            ...newNestedFieldNames,
          ];

          existingOneOf['properties'][propertyKey]['nestedFieldNames'] =
            mergedNestedFieldNames;
        }
      }
    }
  }

  const mergedDefinition: Record<string, unknown> = {
    ...rest,
    properties:
      Object.keys(mergedProperties).length > 0 ? mergedProperties : undefined,
    oneOf: mergedOneOf.length > 0 ? mergedOneOf : undefined,
    required,
    nestedFieldNames:
      mergedNestedFieldNames.size > 0
        ? Array.from(mergedNestedFieldNames)
        : undefined,
  };

  return mergedDefinition;
}

function getTransformedProperty(
  originalPropertyKey: string,
  originalProperty: unknown,
  nameMappingChild: XsdElement | undefined,
  parentNestedAbbrName: string
): Record<string, unknown> {
  if (!isValidObject(originalProperty)) {
    throw new Error(`Invalid property format for "${originalPropertyKey}".`);
  }

  return {
    ...originalProperty,
    name: nameMappingChild ? nameMappingChild.fullName : originalPropertyKey,
    nestedFieldNames: [
      nameMappingChild
        ? nameMappingChild.nestedAbbrName
        : `${parentNestedAbbrName}-${originalPropertyKey}`,
    ],
  };
}

function getTransformedProperties(
  originalProperties: Record<string, unknown>,
  originalRequiredProperties: unknown,
  nameMappings: XsdElement
): {
  properties: Record<string, Record<string, unknown>>;
  requiredProperties: string[] | undefined;
} {
  if (
    !(typeof originalRequiredProperties === 'undefined') &&
    !Array.isArray(originalRequiredProperties)
  ) {
    throw new Error(
      `Invalid required properties format for "${nameMappings.fullName}". Expected undefined or an array.`
    );
  }

  const transformedProperties: Record<string, Record<string, unknown>> = {};
  const transformedRequiredProperties: string[] = [];

  /**
   * The XSD schema contains 'complexType's with a 'simpleContent' element which contains an 'attribute' that is extended by an 'extension'.
   * This is used to depict elements like '<IntrBkSttlmAmt Ccy="EUR">900.00</IntrBkSttlmAmt>' in the schema ('Ccy' is the attribute, the value is the 'extension').
   * In the JSON schema, this is represented by using a 'properties' object that includes both the attribute and the extension, e.g. "properties": { "currency": { "$ref": "..." }, "amount": { "type": "string", ... } }.
   * There is no 'name' linking the 'extension' to the 'amount' property, so we just make sure that when the 'amount' is inevitably not found in the name mappings, there is at least exactly one '[extension]' entry that we extract in the 'extract-xsd-names' script.
   */
  const noOfExtensions = nameMappings.children.filter(
    (child) => child.fullName === '[extension]'
  ).length;
  let noOfPropertiesWithoutNameMapping = 0;

  for (const [propertyKey, propertyValue] of Object.entries(
    originalProperties
  )) {
    const nameMappingChild = nameMappings.children.find(
      (child) => toSnakeCase(child.fullName) === propertyKey
    );

    if (!nameMappingChild) {
      noOfPropertiesWithoutNameMapping++;
      if (noOfPropertiesWithoutNameMapping > noOfExtensions) {
        throw new Error(
          `No name mapping found for "${propertyKey}" in "${nameMappings.fullName}"`
        );
      }
    }

    const transformedProperty = getTransformedProperty(
      propertyKey,
      propertyValue,
      nameMappingChild,
      nameMappings.nestedAbbrName
    );

    const finalPropertyKey =
      nameMappingChild && nameMappingChild.abbrName
        ? nameMappingChild.abbrName
        : propertyKey;

    transformedProperties[finalPropertyKey] = transformedProperty;
    if (
      originalRequiredProperties &&
      originalRequiredProperties.includes(propertyKey)
    ) {
      transformedRequiredProperties.push(finalPropertyKey);
    }
  }

  return {
    properties: transformedProperties,
    requiredProperties:
      transformedRequiredProperties.length > 0
        ? transformedRequiredProperties
        : undefined,
  };
}

function getTransformedDefinition(
  originalDefinition: Record<string, unknown>,
  properties: unknown,
  oneOf: unknown,
  nameMappings: XsdElement
): Record<string, unknown> {
  const transformedDefinition: Record<string, unknown> = {
    ...originalDefinition,
  };

  if (!properties && !oneOf) {
    // This means that this definition is not a complex type with properties or oneOf. We still add the 'nestedFieldNames'.
    transformedDefinition['nestedFieldNames'] = [nameMappings.nestedAbbrName];
  }

  if (isValidObject(properties)) {
    const requiredProperties = originalDefinition['required'];
    const transformedProperties = getTransformedProperties(
      properties,
      requiredProperties,
      nameMappings
    );

    transformedDefinition['properties'] = transformedProperties.properties;
    transformedDefinition['required'] =
      transformedProperties.requiredProperties;
  }

  if (Array.isArray(oneOf)) {
    const transformedOneOf: Record<string, unknown>[] = [];
    for (const oneOfEntry of oneOf) {
      const oneOfProperties = oneOfEntry.properties;
      if (
        !isValidObject(oneOfProperties) ||
        Object.keys(oneOfProperties).length !== 1
      ) {
        throw new Error(
          `Invalid properties format in oneOf entry: ${oneOfEntry}`
        );
      }

      const oneOfRequiredProperties = oneOfEntry.required;

      const transformedProperties = getTransformedProperties(
        oneOfProperties,
        oneOfRequiredProperties,
        nameMappings
      );

      transformedOneOf.push({
        ...oneOfEntry,
        properties: transformedProperties.properties,
        required: transformedProperties.requiredProperties,
      });
    }
    transformedDefinition['oneOf'] = transformedOneOf;
  }

  return transformedDefinition;
}

function getNestedTransformedDefinitionsForProperty(
  originalDefinitions: Record<string, unknown>,
  propertyKey: string,
  originalProperty: unknown,
  nameMappings: XsdElement
): Map<string, Array<Record<string, unknown>>> {
  if (!isValidObject(originalProperty)) {
    throw new Error(`Invalid property format for "${originalProperty}".`);
  }

  const propertyRef = getRef(originalProperty);

  if (!propertyRef) {
    return new Map();
  }

  const nameMappingChild = nameMappings.children.find(
    (child) => toSnakeCase(child.fullName) === propertyKey
  );

  if (!nameMappingChild) {
    throw new Error(
      `No name mapping found for "${propertyKey}" in "${nameMappings.fullName}".`
    );
  }

  return getTransformedDefinitions(
    originalDefinitions,
    propertyRef,
    nameMappingChild
  );
}

function getTransformedDefinitions(
  originalDefinitions: Record<string, unknown>,
  ref: string,
  nameMappings: XsdElement
): Map<string, Array<Record<string, unknown>>> {
  const definition = originalDefinitions[ref];
  if (!isValidObject(definition)) {
    throw new Error(`Invalid definition for "${ref}".`);
  }

  const properties = definition['properties'];
  const oneOf = definition['oneOf'];

  const transformedDefinitionsMap: Map<
    string,
    Array<Record<string, unknown>>
  > = new Map();

  // Transform the definition itself without going through refs
  const transformedDefinition = getTransformedDefinition(
    definition,
    properties,
    oneOf,
    nameMappings
  );

  transformedDefinitionsMap.set(ref, [transformedDefinition]);

  // Go through the refs in 'properties' or 'oneOf' and transform them recursively

  if (isValidObject(properties)) {
    for (const [propertyKey, propertyValue] of Object.entries(properties)) {
      const nestedTransformedDefinitions =
        getNestedTransformedDefinitionsForProperty(
          originalDefinitions,
          propertyKey,
          propertyValue,
          nameMappings
        );

      for (const [key, entries] of nestedTransformedDefinitions) {
        if (!transformedDefinitionsMap.has(key)) {
          transformedDefinitionsMap.set(key, []);
        }
        transformedDefinitionsMap.get(key)?.push(...entries);
      }
    }
  }

  if (Array.isArray(oneOf)) {
    for (const oneOfEntry of oneOf) {
      const properties = oneOfEntry.properties;
      if (!isValidObject(properties) || Object.keys(properties).length !== 1) {
        throw new Error(
          `Invalid properties format in oneOf entry: ${oneOfEntry}`
        );
      }

      const [propertyKey, propertyValue] = Object.entries(properties)[0];

      const nestedTransformedDefinitions =
        getNestedTransformedDefinitionsForProperty(
          originalDefinitions,
          propertyKey,
          propertyValue,
          nameMappings
        );

      for (const [key, entries] of nestedTransformedDefinitions) {
        if (!transformedDefinitionsMap.has(key)) {
          transformedDefinitionsMap.set(key, []);
        }
        transformedDefinitionsMap.get(key)?.push(...entries);
      }
    }
  }

  return transformedDefinitionsMap;
}

export function transformJsonSchema(
  jsonSchema: unknown,
  nameMappings: XsdElement
): Record<string, unknown> {
  if (
    !isValidObject(jsonSchema) ||
    !('properties' in jsonSchema) ||
    !('definitions' in jsonSchema)
  ) {
    throw new Error('Invalid JSON schema format.');
  }

  const { properties, definitions, ...rest } = jsonSchema;

  if (!isValidObject(definitions)) {
    throw new Error('Invalid definitions format.');
  }

  const basePropertyKey = toSnakeCase(nameMappings.fullName);

  if (!isValidObject(properties) || !(basePropertyKey in properties)) {
    throw new Error("Invalid 'properties' format.");
  }

  const baseProperty = properties[basePropertyKey];

  const transformedBaseProperty = getTransformedProperty(
    basePropertyKey,
    baseProperty,
    nameMappings,
    ''
  );

  const transformedProperties: Record<string, unknown> = {
    ...properties,
    [nameMappings.abbrName]: transformedBaseProperty,
  };

  delete transformedProperties[basePropertyKey];

  const basePropertyRef = getRef(transformedBaseProperty);
  if (!basePropertyRef) {
    throw new Error(
      `Invalid base property reference for "${basePropertyKey}".`
    );
  }

  const transformedDefinitions: Map<
    string,
    Array<Record<string, unknown>>
  > = getTransformedDefinitions(definitions, basePropertyRef, nameMappings);

  // The resulting transformedDefinitions contains multiple entries for the same ref if a definition is used in more than one branch.
  // We need to merge them into a single definition per ref combining the different 'nestedFieldName's.
  const mergedDefinitions: Record<string, Record<string, unknown>> = {};
  for (const [ref, entries] of transformedDefinitions) {
    const mergedDefinition: Record<string, unknown> =
      getMergedDefinition(entries);
    mergedDefinitions[ref] = mergedDefinition;
  }

  const transformedSchema: Record<string, unknown> = {
    ...rest,
    properties: transformedProperties,
    definitions: mergedDefinitions,
  };

  return transformedSchema;
}

function getNestedFieldNames(
  definitionReference: string,
  definitions: Record<string, unknown>
): string[] {
  const definition = definitions[definitionReference];
  if (!isValidObject(definition)) {
    throw new Error(`Invalid definition reference '${definitionReference}'.`);
  }

  const properties = definition['properties'];
  const oneOf = definition['oneOf'];

  if (!properties && !oneOf) {
    // If there are no properties or oneOf, we assume this is a simple type with no nested fields.
    const nestedFieldNames = definition['nestedFieldNames'];
    if (!isValidStringArray(nestedFieldNames)) {
      throw new Error(
        `Invalid nestedFieldNames for definition reference '${definitionReference}'.`
      );
    }
    return nestedFieldNames;
  }

  if (isValidObject(properties)) {
    const nestedFieldNames: string[] = [];
    for (const [propertyKey, propertyValue] of Object.entries(properties)) {
      if (!isValidObject(propertyValue)) {
        throw new Error(`Invalid property format for "${propertyKey}".`);
      }

      const propertyRef = getRef(propertyValue);
      if (!propertyRef) {
        // This is the case for 'extension' properties (e.g. 'FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-amount)
        const fieldNames = propertyValue['nestedFieldNames'];
        if (!isValidStringArray(fieldNames)) {
          throw new Error(
            `Invalid nestedFieldNames for property '${propertyKey}'.`
          );
        }
        nestedFieldNames.push(...fieldNames);
        return nestedFieldNames;
      }

      const nestedFields = getNestedFieldNames(propertyRef, definitions);
      nestedFieldNames.push(...nestedFields);
    }
    return nestedFieldNames;
  }

  if (Array.isArray(oneOf)) {
    const nestedFieldNames: string[] = [];
    for (const oneOfEntry of oneOf) {
      const oneOfProperties = oneOfEntry['properties'];
      if (
        !isValidObject(oneOfProperties) ||
        Object.keys(oneOfProperties).length !== 1
      ) {
        throw new Error(
          `Invalid properties format in oneOf entry: ${oneOfEntry}`
        );
      }

      const propertyKey = Object.keys(oneOfProperties)[0];
      const propertyValue = oneOfProperties[propertyKey];

      if (!isValidObject(propertyValue)) {
        throw new Error(`Invalid property format for "${propertyKey}".`);
      }

      const propertyRef = getRef(propertyValue);
      if (!propertyRef) {
        throw new Error(
          `Property "${propertyKey}" does not have a valid reference.`
        );
      }

      const nestedFields = getNestedFieldNames(propertyRef, definitions);
      nestedFieldNames.push(...nestedFields);
    }
    return nestedFieldNames;
  }

  throw new Error(
    `Invalid definition structure for reference '${definitionReference}'.`
  );
}

export function getAllFieldsFromJSONSchema(
  jsonSchema: Record<string, unknown>,
  entryProperty: string
): string[] {
  const properties = jsonSchema['properties'];
  if (!isValidObject(properties)) {
    throw new Error('Invalid JSON schema properties format.');
  }
  if (!(entryProperty in properties)) {
    throw new Error(
      `Entry property "${entryProperty}" not found in properties.`
    );
  }
  const entryPropertyValue = properties[entryProperty];
  if (!isValidObject(entryPropertyValue)) {
    throw new Error(
      `Entry property "${entryProperty}" is not a valid object in properties.`
    );
  }

  const definitionReference = getRef(entryPropertyValue);
  if (!definitionReference) {
    throw new Error(
      `Entry property "${entryProperty}" does not have a valid reference.`
    );
  }

  const definitions = jsonSchema['definitions'];
  if (!isValidObject(definitions)) {
    throw new Error('Invalid JSON schema definitions format.');
  }

  const allFields: Set<string> = new Set(
    getNestedFieldNames(definitionReference, definitions)
  );

  return Array.from(allFields);
}
