import { EXTENSION_SUFFIX, XsdElement } from './types';
import { getRef, isValidObject, isValidStringArray } from './utils';

function findExtensionEntries(nameMappings: XsdElement): XsdElement[] {
  if (nameMappings.abbrName === EXTENSION_SUFFIX) {
    return [nameMappings];
  }

  const extensionEntries: XsdElement[] = [];

  for (const child of nameMappings.children) {
    extensionEntries.push(...findExtensionEntries(child));
  }

  return extensionEntries;
}

function findExtensionSiblings(
  nameMappings: XsdElement,
  nestedAbbrNameOfParent: string
): XsdElement[] {
  if (nameMappings.nestedAbbrName === nestedAbbrNameOfParent) {
    return nameMappings.children;
  }

  const extensionSiblings: XsdElement[] = [];
  for (const child of nameMappings.children) {
    const siblings = findExtensionSiblings(child, nestedAbbrNameOfParent);
    if (siblings.length > 0) {
      extensionSiblings.push(...siblings);
    }
  }

  return extensionSiblings;
}

function getProperties(properties: unknown): Record<string, unknown>[] {
  if (!isValidObject(properties)) {
    throw new Error(`Invalid properties format: ${JSON.stringify(properties)}`);
  }

  const result: Record<string, unknown>[] = [];
  for (const [key, value] of Object.entries(properties)) {
    if (!isValidObject(value)) {
      throw new Error(`Invalid property for ${key}: expected an object.`);
    }
    result.push(value);
  }

  return result;
}

function getDefinitions(
  jsonSchema: Record<string, unknown>
): Record<string, unknown> {
  if (
    !isValidObject(jsonSchema) ||
    !('properties' in jsonSchema) ||
    !('definitions' in jsonSchema)
  ) {
    throw new Error('Invalid JSON schema format.');
  }

  const { definitions } = jsonSchema;

  if (!isValidObject(definitions)) {
    throw new Error('Invalid definitions format.');
  }

  return definitions;
}

function getPropertiesToSearch(
  definitions: Record<string, unknown>
): Record<string, unknown>[] {
  const allPropertiesToSearch: Record<string, unknown>[] = [];

  for (const [key, value] of Object.entries(definitions)) {
    if (!isValidObject(value)) {
      throw new Error(`Invalid definition for ${key}: expected an object.`);
    }
    if (!('type' in value)) {
      throw new Error(`Invalid definition for ${key}: missing type.`);
    }

    if (
      value['type'] === 'string' ||
      value['type'] === 'array' ||
      value['type'] === 'boolean'
    ) {
      continue;
    }

    if (!('properties' in value) && !('oneOf' in value)) {
      throw new Error(
        `Invalid definition for ${key}: missing properties or oneOf.`
      );
    }

    const propertiesToSearch: Record<string, unknown>[] = [];
    if ('properties' in value) {
      propertiesToSearch.push(...getProperties(value['properties']));
    } else {
      if (!Array.isArray(value['oneOf'])) {
        throw new Error(
          `Invalid definition for ${key}: oneOf must be an array.`
        );
      }
      for (const oneOfItem of value['oneOf']) {
        if (!isValidObject(oneOfItem) || !('properties' in oneOfItem)) {
          throw new Error(
            `Invalid oneOf item for ${key}: expected an object with properties.`
          );
        }
        propertiesToSearch.push(...getProperties(oneOfItem['properties']));
      }
    }
    allPropertiesToSearch.push(...propertiesToSearch);
  }
  return allPropertiesToSearch;
}

function findChildrenNames(
  definitions: Record<string, unknown>,
  allProperties: Record<string, unknown>[],
  parent: string
): string[] {
  for (const property of allProperties) {
    if (
      !('nestedFieldNames' in property) ||
      !Array.isArray(property['nestedFieldNames'])
    ) {
      throw new Error(
        `Invalid property: expected array 'nestedFieldNames' in ${JSON.stringify(
          property
        )}`
      );
    }

    if (property['nestedFieldNames'].includes(parent)) {
      const ref = getRef(property);
      if (!ref) {
        throw new Error(
          `Property ${JSON.stringify(property)} does not have a valid ref.`
        );
      }
      const referencedDefinition = definitions[ref];
      if (
        !isValidObject(referencedDefinition) ||
        !('type' in referencedDefinition) ||
        referencedDefinition['type'] !== 'object' ||
        !('properties' in referencedDefinition) ||
        !isValidObject(referencedDefinition['properties'])
      ) {
        throw new Error(
          `Referenced definition for ${ref} is not a valid object: ${JSON.stringify(
            referencedDefinition
          )}`
        );
      }
      const properties = referencedDefinition['properties'];
      const childrenNames: string[] = [];
      for (const [key, value] of Object.entries(properties)) {
        if (
          !isValidObject(value) ||
          !('nestedFieldNames' in value) ||
          !isValidStringArray(value['nestedFieldNames'])
        ) {
          throw new Error(
            `Invalid property for ${key} in ${ref}: expected an object with nestedFieldNames.`
          );
        }
        const nestedFieldNamesWithoutPropertyKey = value[
          'nestedFieldNames'
        ].map((name) => name.replace(`-${key}`, ''));
        if (!nestedFieldNamesWithoutPropertyKey.includes(parent)) {
          throw new Error(
            `Property ${key} in ${ref} does not have the expected parent ${parent}.`
          );
        }
        childrenNames.push(parent + '-' + key);
      }
      return childrenNames;
    }
  }

  throw new Error(
    `No children names found for parent ${parent} in definitions.`
  );
}

function findRemainingChildName(
  extensionSiblings: Record<string, XsdElement[]>,
  parent: string,
  childrenNames: string[]
): string {
  const extensionSiblingsForParent: string[] = extensionSiblings[parent].map(
    (entry) => entry.nestedAbbrName
  );
  if (!extensionSiblingsForParent || extensionSiblingsForParent.length === 0) {
    throw new Error(
      `No extension siblings found for parent ${parent}. This should not happen.`
    );
  }
  if (childrenNames.length !== extensionSiblingsForParent.length) {
    throw new Error(
      `Number of children names (${childrenNames.length}) does not match number of extension siblings (${extensionSiblingsForParent.length}) for parent ${parent}.`
    );
  }
  const remainingChildNames: string[] = [];
  for (const childName of childrenNames) {
    if (!extensionSiblingsForParent.includes(childName)) {
      remainingChildNames.push(childName);
    }
  }
  if (remainingChildNames.length !== 1) {
    throw new Error(
      `Expected exactly one remaining child name for parent ${parent}, found ${remainingChildNames.length}.`
    );
  }

  return remainingChildNames[0];
}

function getReplacedNameMappings(
  nameMappings: XsdElement,
  extensionEntries: XsdElement[]
): XsdElement {
  const newNameMappings: XsdElement = { ...nameMappings, children: [] };
  for (const child of nameMappings.children) {
    if (child.abbrName === EXTENSION_SUFFIX) {
      throw new Error(
        `Unexpected child with abbrName "${EXTENSION_SUFFIX}" in nameMappings. This should have been replaced by the replaceExtensionNames function.`
      );
    }
    newNameMappings.children.push(
      getReplacedNameMappings(child, extensionEntries)
    );
  }
  return newNameMappings;
}

/**
 * The 'extractXsdElementMappings' function above only looks at the XSD schema to extract name mappings. In case of a 'simpleContent' block with a 'base' attribute, it adds a generic "[extension]" to the name mappings output.
 * In the JSON schema, these fields are named and thus we can replace the generic "[extension]" with the actual name from the JSON schema.
 * @param nameMappings the original name mappings including the generic "[extension]" entries
 * @param jsonSchema the JSON schema that contains the actual names for the "[extension]" entries
 * @returns a new nameMappings object with the "[extension]" entries replaced by the actual names
 */
export function replaceExtensionNames(
  nameMappings: XsdElement,
  jsonSchema: Record<string, unknown>
): XsdElement {
  // Find all entries that have an abbrName of "[extension]"
  const extensionEntries: XsdElement[] = findExtensionEntries(nameMappings);

  // For each "[extension]" entry, find the parent with all siblings.
  const nestedAbbrNamesOfParents: string[] = extensionEntries.map((entry) =>
    entry.nestedAbbrName.replace(`-${EXTENSION_SUFFIX}`, '')
  );
  const extensionSiblings: Record<string, XsdElement[]> = {};
  for (const nestedAbbrName of nestedAbbrNamesOfParents) {
    extensionSiblings[nestedAbbrName] = findExtensionSiblings(
      nameMappings,
      nestedAbbrName
    );
  }

  // For each parent, find the 'name' of all child elements in the JSON schema.
  const definitions = getDefinitions(jsonSchema);
  const allProperties = getPropertiesToSearch(definitions);
  const jsonSchemaNames: Record<string, string[]> = {};
  for (const parent in extensionSiblings) {
    jsonSchemaNames[parent] = findChildrenNames(
      definitions,
      allProperties,
      parent
    );
  }

  // Go through the children of the parent element and find the one that is not listed in the "[extension]" siblings.
  for (const [parent, childrenNames] of Object.entries(jsonSchemaNames)) {
    const selectedChildName = findRemainingChildName(
      extensionSiblings,
      parent,
      childrenNames
    );
    const selectedChildNameSuffix = selectedChildName.split('-').slice(-1)[0];

    // Replace the "[extension]" entry with the actual name from the JSON schema.
    const extensionEntry = extensionEntries.find(
      (entry) => entry.nestedAbbrName === `${parent}-${EXTENSION_SUFFIX}`
    );
    if (!extensionEntry) {
      throw new Error(
        `No extension entry found for parent ${parent} with nestedAbbrName ${parent}-${EXTENSION_SUFFIX}.`
      );
    }

    extensionEntry.nestedAbbrName = selectedChildName;
    extensionEntry.fullName = selectedChildNameSuffix;
    extensionEntry.abbrName = selectedChildNameSuffix;
  }

  // Make sure all "[extension]" entries were replaced.
  const remainingExtensionEntries = extensionEntries.filter(
    (entry) => entry.abbrName === EXTENSION_SUFFIX
  );
  if (remainingExtensionEntries.length > 0) {
    throw new Error(
      `Not all "[extension]" entries were replaced. Remaining entries: ${JSON.stringify(
        remainingExtensionEntries,
        null,
        2
      )}`
    );
  }

  // Return the modified nameMappings with the "[extension]" entries replaced.
  return getReplacedNameMappings(nameMappings, extensionEntries);
}
